import { Ionicons } from "@expo/vector-icons";
import { Redirect, router } from "expo-router";
import {
  ImageBackground,
  StatusBar,
  StyleSheet,
  Text,
  TouchableOpacity,
  View
} from "react-native";
import { useAuthStore } from "../lib/store";

export default function WelcomeScreen() {
  const { auth } = useAuthStore();

  const getStarted = async () => {
    router.push("/onboarding");
  };

  const alreadyHaveAccount = async () => {
    router.push("/login");
  };

  if (auth.status === "authenticated") {
    return <Redirect href="/home" />;
  }

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" />
      <ImageBackground
        source={require("../assets/images/background.png")}
        style={styles.backgroundImage}
        resizeMode="cover"
      >
        <View style={styles.content}>
          <View style={styles.header}>
          </View>

          {(auth.status === "unauthenticated" || auth.status === "idle") && (
            <View style={styles.footer}>
              <TouchableOpacity style={styles.primaryButton} onPress={getStarted}>
                <Text style={styles.primaryButtonText}>Get Started</Text>
                <Ionicons
                  name="arrow-forward"
                  size={24}
                  color="#FFFFFF"
                  style={styles.buttonIcon}
                />
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.secondaryButton}
                onPress={alreadyHaveAccount}
              >
                <Text style={styles.secondaryButtonText}>
                  Already have an account?
                </Text>
                <Text style={styles.loginText}>Log in here</Text>
              </TouchableOpacity>

              <View style={styles.poweredByContainer}>
                <Ionicons
                  name="flash"
                  size={16}
                  color="#FFFFFF"
                  style={styles.poweredByIcon}
                />
                <Text style={styles.poweredBy}>
                  Powered by AI for smarter travel
                </Text>
              </View>
            </View>
          )}

          {auth.status === "loading" && (
            <View style={styles.footer}>
              <Text style={styles.loadingText}>Loading...</Text>
            </View>
          )}
        </View>
      </ImageBackground>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#2196F3",
  },
  backgroundImage: {
    flex: 1,
    width: "100%",
    height: "100%",
  },
  content: {
    flex: 1,
    justifyContent: "space-between",
    padding: 24,
  },
  header: {
    marginTop: "15%",
    alignItems: "center",
  },
  logoContainer: {
    width: 120,
    height: 120,
    justifyContent: "center",
    alignItems: "center",
    marginBottom: 24,
  },
  logo: {
    width: "100%",
    height: "100%",
  },
  title: {
    fontSize: 36,
    fontWeight: "800",
    color: "#FFFFFF",
    textAlign: "center",
    marginBottom: 12,
    letterSpacing: 0.5,
  },
  subtitle: {
    fontSize: 22,
    fontWeight: "600",
    color: "#FFFFFF",
    textAlign: "center",
    opacity: 0.9,
    marginBottom: 16,
  },
  description: {
    fontSize: 17,
    color: "#FFFFFF",
    textAlign: "center",
    opacity: 0.8,
    lineHeight: 24,
    maxWidth: "80%",
  },
  footer: {
    marginBottom: "10%",
    alignItems: "center",
    width: "100%",
  },
  primaryButton: {
    flexDirection: "row",
    backgroundColor: "#1565C0",
    paddingVertical: 16,
    paddingHorizontal: 32,
    borderRadius: 30,
    alignItems: "center",
    justifyContent: "center",
    width: "100%",
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 5,
    elevation: 8,
    marginBottom: 20,
  },
  primaryButtonText: {
    fontSize: 18,
    fontWeight: "700",
    color: "#FFFFFF",
    marginRight: 8,
  },
  buttonIcon: {
    marginLeft: 4,
  },
  secondaryButton: {
    alignItems: "center",
    marginBottom: 32,
  },
  secondaryButtonText: {
    fontSize: 15,
    color: "#FFFFFF",
    opacity: 0.9,
    marginBottom: 4,
  },
  loginText: {
    fontSize: 16,
    fontWeight: "600",
    color: "#FFFFFF",
    textDecorationLine: "underline",
  },
  poweredByContainer: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "rgba(255, 255, 255, 0.1)",
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 20,
  },
  poweredByIcon: {
    marginRight: 6,
  },
  poweredBy: {
    fontSize: 14,
    color: "#FFFFFF",
    opacity: 0.9,
  },
  loadingText: {
    fontSize: 16,
    color: "#FFFFFF",
    opacity: 0.8,
  },
});
