{"name": "itrp-mobile", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"start": "expo start -c", "reset-project": "node ./scripts/reset-project.js", "android": "expo run:android --variant googlePlayDebug", "ios": "expo run:ios", "web": "expo start --web", "prebuild:dev:ios": "APP_VARIANT=development  expo prebuild --platform ios --clean", "prebuild:dev:android": "APP_VARIANT=development  expo prebuild --platform android --clean", "prebuild:preview:ios": "APP_VARIANT=preview   expo prebuild --platform ios --clean", "prebuild:preview:android": "APP_VARIANT=preview   expo prebuild --platform android --clean", "prebuild:ios": "expo prebuild --platform ios --clean", "prebuild:android": "expo prebuild --platform android --clean", "test": "jest --watchAll", "lint": "expo lint", "format": "npx prettier --write .", "fix-swift": "./.patches/apply-swift-fixes.sh", "check-swift": "./.patches/check-swift-issues.sh"}, "jest": {"preset": "jest-expo"}, "dependencies": {"@expo/vector-icons": "^14.1.0", "@react-native-community/datetimepicker": "8.4.1", "@react-native-community/slider": "4.5.6", "@react-native-google-signin/google-signin": "^13.2.0", "@react-native-picker/picker": "2.11.1", "@react-navigation/bottom-tabs": "^7.2.0", "@react-navigation/native": "^7.0.14", "@react-navigation/native-stack": "^7.0.0", "@rnmapbox/maps": "^10.1.39", "axios": "^1.7.9", "expo": "53.0.19", "expo-auth-session": "~6.2.1", "expo-blur": "~14.1.5", "expo-constants": "~17.1.4", "expo-crypto": "~14.1.5", "expo-dev-client": "~5.2.4", "expo-font": "~13.3.0", "expo-haptics": "~14.1.4", "expo-linear-gradient": "~14.1.5", "expo-linking": "~7.1.7", "expo-location": "~18.1.6", "expo-notifications": "~0.31.4", "expo-router": "~5.1.3", "expo-splash-screen": "~0.30.10", "expo-status-bar": "~2.2.3", "expo-symbols": "~0.4.5", "expo-system-ui": "~5.0.10", "expo-updates": "~0.28.17", "expo-web-browser": "~14.2.0", "openai": "^4.84.0", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.5", "react-native-draggable-flatlist": "^4.0.1", "react-native-gesture-handler": "~2.24.0", "react-native-iap": "^12.16.2", "react-native-linear-gradient": "^2.8.3", "react-native-mmkv": "^3.2.0", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-walkthrough-tooltip": "^1.6.0", "react-native-web": "^0.20.0", "react-native-webview": "13.13.5", "socket.io-client": "^4.8.1", "zustand": "^5.0.2"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/jest": "^29.5.12", "@types/react": "~19.0.10", "babel-plugin-module-resolver": "^5.0.2", "eslint": "^9.0.0", "eslint-config-expo": "~9.2.0", "jest": "^29.2.1", "jest-expo": "~53.0.9", "nativewind": "^4.1.23", "patch-package": "^8.0.0", "tailwindcss": "^3.3.2", "typescript": "^5.3.3"}, "private": true}