export interface Activity {
  id: string;
  type?: "activity" | "meal";
  name?: string;
  description?: string;
  location?: string;
  startTime?: string;
  endTime?: string;
  cost?: number;
  icon?: string;
  coordinates?: {
    lat: number;
    lng: number;
  };
  // Common optional fields
  imageUrl?: string;
  source?: "deepseek" | "user";
  rating?: number;
  tags?: string[];

  // Activity-specific fields
  duration?: string;

  // Meal-specific fields
  mealType?: "breakfast" | "lunch" | "dinner";
  restaurant?: string;



  // Activity completion status
  completed?: boolean;

  // City where the activity takes place
  city: string;

  // Day number and date (added when fetching activities by city)
  dayNumber?: number;
  date?: string;
}

export interface DailyItinerary {
  day: number;
  date: string;
  activities: Activity[];
}

export interface CostBreakdown {
  activities: number;
  meals: number;
  total_estimated_cost: number;
}

export type BudgetType = "budget" | "normal" | "luxury";

export interface TripDetails {
  destination: string;
  startDate: string;
  endDate: string;
  totalDays: number;
  budget: BudgetType;
  intensity: number;
  travelType: string;
  arrivalCity: string;
  departureCity: string;
  arrivalMode: "Air" | "Land" | "Sea";
  departureMode: "Air" | "Land" | "Sea";
  arrivalTime: string;
  departureTime: string;
  wakeUpTime: string;
  sleepTime: string;
  cuisinePreferences?: string[];
  mustVisitCities: string[];
  additionalRequirements?: string;
  interests: string[];
  people: {
    adults: number;
    children: number;
  };
  imageUrl?: string;
}

export interface Interest {
  id: string;
  name: string;
  icon: string;
}

export interface User {
  _id: string;
  email: string;
  name: string;
  picture: string;
}

export interface AuthState {
  status: "idle" | "loading" | "authenticated" | "unauthenticated";
  loadingType?: "google" | "email" | "logout";
  user?: User;
  token?: string;
  error?: string;
}

export interface AuthStore {
  auth: AuthState;
  setAuth: (auth: AuthState) => void;
}

export interface TripStore {
  tripDetails: TripDetails;
  customInterests: Interest[];
  setTripDetails: (details: TripDetails) => void;
  addInterest: (interest: Interest) => void;
  removeInterest: (interestId: string) => void;
  reset: () => void;
}

export interface DayProgress {
  day: number;
  is_generating: boolean;
  started_at: Date | null;
  tries: number;
  finished_at: Date | null;
  error: string | null;
}

export interface DbTrip {
  _id: string;
  name: string;
  tripDetails: TripDetails;
  itinerary: DailyItinerary[];
  customItinerary: DailyItinerary[];
  status: "ready" | "in_progress";
  currentBuffer: string;
  currentDayProgress: number;
  retryCount: number;
  lastRetryDate: Date;
  createdAt?: string;
  updatedAt?: string;
  costBreakdown: CostBreakdown;
  additionalTips: string[];
  daysProgress: DayProgress[];
  isFavorite?: boolean;
  isArchived?: boolean;
  isExample?: boolean;
  highLevelPlan?: string;
}

export enum TripStreamUpdateType {
  IN_PROGRESS = "in_progress",
  READY = "ready",
  ERROR = "error",
  INSUFFICIENT_BALANCE = "insufficient_balance",
}
