import { Platform } from "react-native";
import { Activity } from "./types";
export const API_URL =
  Platform.OS === "android" ? "http://********:3000" : "http://127.0.0.1:3000";

export const TRAVEL_TYPES = [
  "Select Type",
  "Luxury",
  "Roadtrip",
  "Budget",
  "Cultural",
  "Adventure",
  "Family",
  "Romantic",
  "Business",
  "Solo",
] as const;

export const TRANSPORT_MODES = ["Air", "Land", "Sea"] as const;

export const BUDGET_OPTIONS = [
  { label: "Budget", value: "budget", description: "Low-cost options" },
  { label: "Normal", value: "normal", description: "Mid-range options" },
  { label: "Luxury", value: "luxury", description: "High-end options" },
] as const;
export const CUISINE_PREFERENCES = [
  "No Preference",
  "Local",
  "Traditional",
  "International",
  "Vegetarian",
  "Vegan",
  "Halal",
  "Kosher",
  "Gluten-Free",
  "Dairy-Free",
  "Sugar-Free",
] as const;

export const INTERESTS = [
  { id: "1", name: "Museums", icon: "library" },
  { id: "2", name: "Food", icon: "restaurant" },
  { id: "3", name: "Shopping", icon: "cart" },
  { id: "4", name: "Nature", icon: "leaf" },
  { id: "5", name: "History", icon: "time" },
  { id: "6", name: "Art", icon: "color-palette" },
  { id: "7", name: "Music", icon: "musical-notes" },
  { id: "8", name: "Sports", icon: "basketball" },
  { id: "9", name: "Nightlife", icon: "wine" },
  { id: "10", name: "Architecture", icon: "business" },
  { id: "11", name: "Local Culture", icon: "people" },
  { id: "12", name: "Photography", icon: "camera" },
  { id: "13", name: "Adventure", icon: "compass" },
  { id: "14", name: "Wellness", icon: "fitness" },
  { id: "15", name: "Technology", icon: "hardware-chip" },
  { id: "16", name: "Festivals", icon: "calendar" },
  { id: "17", name: "Beaches", icon: "sunny" },
  { id: "18", name: "Theme Parks", icon: "color-wand" },
  { id: "19", name: "Wildlife", icon: "paw" },
  { id: "20", name: "Wine Tasting", icon: "wine" },
  { id: "21", name: "Street Food", icon: "fast-food" },
  { id: "22", name: "Local Markets", icon: "cart" },
];

export const INTERESTS_ICONS = {
  Museums: "library",
  Food: "restaurant",
  Shopping: "cart",
  Nature: "leaf",
  History: "time",
  Art: "color-palette",
  Music: "musical-notes",
  Sports: "basketball",
  Nightlife: "wine",
  Architecture: "business",
  "Local Culture": "people",
  Photography: "camera",
  Adventure: "compass",
  Wellness: "fitness",
  Technology: "hardware-chip",
  Festivals: "calendar",
  Beaches: "sunny",
  "Theme Parks": "color-wand",
  Wildlife: "paw",
  "Wine Tasting": "wine",
  "Street Food": "fast-food",
  "Local Markets": "cart",
};



export const MOCK_ITINERARY: { [key: string]: Activity[] } = {
  "Day 1": [
    {
      id: "1",
      startTime: "09:00",
      name: "Museum Visit",
      location: "Metropolitan Museum",
      duration: "2h",
      description: "",
      cost: 0,
      imageUrl: "",
      type: "activity",
      endTime: "",
      icon: "library",
      city: "New York",
    },
    {
      id: "2",
      startTime: "12:00",
      name: "Lunch",
      location: "Local Restaurant",
      duration: "1h",
      description: "",
      cost: 0,
      imageUrl: "",
      type: "activity",
      endTime: "",
      icon: "restaurant",
      city: "New York",
    },
  ],
  "Day 2": [
    {
      id: "3",
      startTime: "10:00",
      name: "City Tour",
      location: "Downtown",
      duration: "3h",
      description: "",
      cost: 0,
      imageUrl: "",
      type: "activity",
      endTime: "",
      icon: "map",
      city: "New York",
    },
  ],
};

export const googleConfig = {
  iosClientId:
    "994695515433-9ulrkcdr7o8au00b7cchq5o3vrq1bqq3.apps.googleusercontent.com",
  androidClientId:
    "994695515433-9l8f1lfn3ho2p8vsejasb89k1.apps.googleusercontent.com",
  webClientId:
    "994695515433-9l8f1lfn3ho2p8vsejasb89k1.apps.googleusercontent.com",
};

export const THRESHOLD_STUCK_MINUTES = 3;
