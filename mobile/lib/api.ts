import axios from "axios";

const API_URL = "http://localhost:3000";

const api = axios.create({
  baseURL: API_URL,
  headers: {
    "Content-Type": "application/json",
  },
});

export interface GenerateActivitiesRequest {
  country: string;
  city: string;
  travelType:
    | "Budget"
    | "Luxury"
    | "Roadtrip"
    | "Family"
    | "Cultural"
    | "Adventure"
    | "Romantic"
    | "Business"
    | "Solo";
  intensity: number;
  additionalRequirements?: string;
}

export interface GenerateRestaurantsRequest {
  country: string;
  city: string;
  priceRange: "Budget" | "Moderate" | "Luxury";
  cuisinePreferences: string[];
  additionalRequirements?: string;
}

export interface GenerateHotelsRequest {
  country: string;
  city: string;
  priceRange: "Budget" | "Moderate" | "Luxury";
  starRating: number;
  amenities: string[];
  additionalRequirements?: string;
}

export const generateActivities = async (data: GenerateActivitiesRequest) => {
  const response = await api.post("/trips/generate-activities", data);
  return response.data;
};

export const generateRestaurants = async (data: GenerateRestaurantsRequest) => {
  const response = await api.post("/trips/generate-restaurants", data);
  return response.data;
};

export const generateHotels = async (data: GenerateHotelsRequest) => {
  const response = await api.post("/trips/generate-hotels", data);
  return response.data;
};
