# GitHub Actions Setup for Mobile App

This document outlines the setup required for the GitHub Actions workflows for the mobile app CI/CD pipeline.

## Required GitHub Secrets

### Expo/EAS Secrets

1. **EXPO_TOKEN**
   - Go to [Expo Access Tokens](https://expo.dev/accounts/[account]/settings/access-tokens)
   - Create a new token with appropriate permissions
   - Add to GitHub repository secrets

### Apple App Store Secrets

2. **APPLE_ID**

   - Your Apple ID email address
   - Used for App Store Connect authentication

3. **ASC_APP_ID**

   - Your App Store Connect app ID
   - Found in App Store Connect under App Information

4. **APPLE_TEAM_ID**
   - Your Apple Developer Team ID
   - Found in Apple Developer Account settings

### Google Play Store Secrets

5. **GOOGLE_SERVICE_ACCOUNT_JSON**
   - Service account JSON key for Google Play Console
   - Create in Google Cloud Console with Play Console access
   - Base64 encode the JSON file content

## EAS Configuration

### Build Profiles

The `eas.json` file defines three build profiles:

- **development**: For internal testing with development client
- **preview**: For internal distribution and testing
- **production**: For App Store and Play Store releases

### Update Channels

OTA updates are configured with separate channels:

- `production`: For production releases
- `preview`: For preview builds
- `development`: For development builds

## Workflow Overview

### CI Workflow (`mobile-ci.yml`)

Runs on:

- Pull requests to main/develop
- Pushes to main/develop (when mobile files change)

Steps:

1. Install dependencies with caching
2. Run `expo install --fix`
3. Run prebuild for both iOS and Android
4. Lint code
5. Type check with TypeScript
6. Run tests with coverage

### Preview Workflow (`mobile-preview.yml`)

Runs on:

- Pull requests to main (when mobile files change)
- Manual workflow dispatch

Steps:

1. Build preview versions for iOS and Android
2. Publish OTA update to preview channel
3. Comment on PR with build information

### CD Workflow (`mobile-cd.yml`)

Runs on:

- Tag pushes matching `v*.*.*` pattern

Jobs:

1. **Build**: Creates production builds for iOS and Android
2. **Submit**: Submits builds to App Store and Play Store
3. **Update**: Publishes OTA update to production channel

Features:

- Parallel job execution for faster deployment
- Build ID tracking between jobs
- Continue on error for store submissions
- Separate OTA update job

## Setup Instructions

### 1. Configure EAS Project

```bash
cd mobile
npx eas login
npx eas build:configure
```

### 2. Set up App Store Connect

1. Create app in App Store Connect
2. Configure app metadata and screenshots
3. Set up App Store Connect API key in EAS

### 3. Set up Google Play Console

1. Create app in Google Play Console
2. Create service account in Google Cloud Console
3. Grant Play Console access to service account
4. Download service account JSON key

### 4. Configure GitHub Secrets

Add all required secrets to your GitHub repository:

- Repository Settings → Secrets and variables → Actions
- Add each secret listed above

### 5. Configure EAS Credentials

```bash
# Configure iOS credentials
npx eas credentials:configure --platform ios

# Configure Android credentials
npx eas credentials:configure --platform android
```

### 6. Test Builds

```bash
# Test development build
npx eas build --platform all --profile development

# Test production build
npx eas build --platform all --profile production
```

## Environment Variables

The workflows use the `APP_VARIANT` environment variable to configure different build variants:

- `development`: Development builds with dev bundle identifier
- `preview`: Preview builds for internal testing
- `production`: Production builds for store release

## Caching

The workflows include caching for:

- Node.js dependencies (`package-lock.json`)
- Expo/EAS CLI tools

## Troubleshooting

### Common Issues

1. **Build failures**: Check EAS build logs for detailed error messages
2. **Credential issues**: Ensure all certificates and provisioning profiles are valid
3. **Submission failures**: Verify App Store Connect and Play Console configurations

### Useful Commands

```bash
# Check EAS project status
npx eas project:info

# View build logs
npx eas build:list

# Check update status
npx eas update:list

# View credentials
npx eas credentials
```

## Release Process

### Creating a Release

1. **Prepare the release**:

   ```bash
   # Update version in package.json
   npm version patch|minor|major

   # Update app.config.js version if needed
   # Commit changes
   git add .
   git commit -m "chore: bump version to v1.0.1"
   ```

2. **Create and push tag**:

   ```bash
   git tag v1.0.1
   git push origin main --tags
   ```

3. **Monitor the deployment**:
   - Check GitHub Actions for build progress
   - Monitor EAS dashboard for build status
   - Verify store submissions

### Manual Builds

You can trigger preview builds manually:

1. Go to Actions tab in GitHub
2. Select "Mobile Preview Build" workflow
3. Click "Run workflow"
4. Choose platform (iOS, Android, or all)

## Security Notes

- Never commit sensitive credentials to the repository
- Use GitHub secrets for all sensitive data
- Regularly rotate access tokens and API keys
- Review and audit access permissions regularly
- Store service account keys securely
- Use environment-specific configurations

## Performance Optimization

### Caching Strategy

The workflows implement caching for:

- Node.js dependencies (`package-lock.json`)
- Expo CLI and EAS CLI installations
- Build artifacts where possible

### Resource Classes

EAS build resource classes are configured for optimal performance:

- **m-medium**: For iOS builds (better performance for Xcode)
- **medium**: For Android builds (sufficient for Gradle builds)

### Parallel Execution

The CD workflow uses parallel jobs to:

- Build iOS and Android simultaneously
- Submit to stores in parallel
- Publish OTA updates independently

## Monitoring and Debugging

### Build Logs

- GitHub Actions logs: Available in the Actions tab
- EAS build logs: `npx eas build:list` and click on build ID
- Store submission logs: Available in EAS dashboard

### Common Build Failures

1. **Certificate/Provisioning Profile Issues**:

   ```bash
   npx eas credentials:configure --platform ios
   ```

2. **Android Keystore Issues**:

   ```bash
   npx eas credentials:configure --platform android
   ```

3. **Dependency Issues**:
   - Clear node_modules and reinstall
   - Check for platform-specific dependency conflicts
   - Verify Expo SDK compatibility

### Notifications

Set up GitHub notifications for:

- Failed workflow runs
- Successful deployments
- Store submission status
