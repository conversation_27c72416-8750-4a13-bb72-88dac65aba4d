import React from "react";
import { View, Text, TouchableOpacity, StyleSheet } from "react-native";
import { Ionicons } from "@expo/vector-icons";

interface OnboardingHeaderProps {
  currentStep: number;
  stepTitles: string[];
  onBack: () => void;
}

const OnboardingHeader: React.FC<OnboardingHeaderProps> = ({
  currentStep,
  stepTitles,
  onBack,
}) => {
  return (
    <View style={styles.header}>
      {currentStep > 0 && (
        <TouchableOpacity style={styles.backButton} onPress={onBack}>
          <Ionicons name="chevron-back" size={24} color="#2196F3" />
        </TouchableOpacity>
      )}
      <View style={styles.headerTextContainer}>
        <Text style={styles.headerTitle}>{stepTitles[currentStep]}</Text>
        <Text style={styles.headerSubtitle}>
          Step {currentStep + 1} of {stepTitles.length}
        </Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  header: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: "#fff",
    borderBottomWidth: 1,
    borderBottomColor: "#eee",
  },
  headerTextContainer: {
    flex: 1,
    marginLeft: 12,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: "600",
    color: "#333",
  },
  headerSubtitle: {
    fontSize: 14,
    color: "#666",
    marginTop: 2,
  },
  backButton: {
    padding: 8,
    borderRadius: 8,
    backgroundColor: "#f5f5f5",
  },
});

export default OnboardingHeader;
