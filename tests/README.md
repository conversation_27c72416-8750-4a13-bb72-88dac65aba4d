# Payment System Test Suite

This directory contains comprehensive test scenarios for the payment system, designed to validate all aspects of the in-app purchase and subscription functionality.

## Overview

The test suite covers:
- ✅ Day refill purchases (consumable products)
- ✅ Subscription upgrades and downgrades
- ✅ Subscription renewals and billing cycles
- ✅ Grace period handling for failed payments
- ✅ Restore purchases functionality
- ✅ Error handling and recovery scenarios
- ✅ Multiple purchase scenarios
- ✅ Network failure simulation

## Running the Tests

### Prerequisites
- Bun runtime installed
- Backend services running (or mocked)

### Execute Test Suite
```bash
# Run all payment scenarios
bun run tests/payment-scenarios.ts

# Or from project root
cd tests && bun payment-scenarios.ts
```

## Test Scenarios

### 1. Day Refill Purchase
Tests the complete flow of purchasing consumable day packs:
- Product loading from backend
- Store transaction simulation
- Backend validation
- Balance update verification

### 2. Subscription Upgrade
Tests upgrading from Free to Pro plan:
- Subscription product selection
- Store subscription flow
- Plan activation
- Balance and status updates

### 3. Subscription Renewal
Simulates monthly subscription renewal:
- Automatic renewal processing
- Receipt validation
- Continued service access

### 4. Grace Period Handling
Tests failed payment scenarios:
- Payment failure simulation
- Grace period activation
- Service continuity during grace period

### 5. Restore Purchases
Tests purchase restoration on new devices:
- Historical purchase retrieval
- Validation of restored purchases
- Balance and subscription restoration

### 6. Failed Payment Recovery
Tests error handling and retry mechanisms:
- Initial payment failure
- Error handling
- Successful retry processing

### 7. Subscription Cancellation
Tests subscription cancellation flow:
- Cancellation processing
- End-of-period access retention
- Status updates

### 8. Multiple Day Refill Purchases
Tests consecutive day refill purchases:
- Multiple purchase processing
- Cumulative balance updates
- Transaction sequencing

### 9. Subscription Downgrade
Tests downgrading from Premium to Pro:
- Downgrade processing
- Billing cycle considerations
- Service level adjustments

### 10. Error Handling and Recovery
Comprehensive error scenario testing:
- Network timeout handling
- Invalid receipt processing
- Recovery mechanisms

## Test Output

The test suite provides detailed console output with:
- 🧪 Test execution status
- 📡 API call logging
- 🏪 Store transaction simulation
- ⏱️ Timing information
- 📊 Summary statistics

### Sample Output
```
🚀 Starting Payment System Test Suite
[14:30:15] INFO: Testing comprehensive payment scenarios with 20-second delays between steps

🧪 Running test: Day Refill Purchase (5 days)
[14:30:16] INFO: 📡 API Call: GET /payments/products
[14:30:17] INFO: 📥 Response: {...}
[14:30:17] INFO: 🏪 Simulating purchase for product: com.aiplanmytrip.days.refill_5
[14:30:19] INFO: 📡 API Call: POST /payments/validate
✅ Test PASSED: Day Refill Purchase (5 days) (3245ms)

📊 TEST SUMMARY
============================================================
Total Tests: 10
✅ Passed: 10
❌ Failed: 0
⏱️  Total Time: 45230ms
============================================================
```

## Mock Backend Routes

The test suite includes comprehensive mock backend responses for:

### Product Configuration
- `/payments/products` - Returns available products and pricing
- Includes both subscription and consumable products
- Platform-specific product IDs

### Payment Validation
- `/payments/validate` - Validates purchase receipts
- Handles both subscription and consumable purchases
- Returns transaction confirmation

### Purchase Restoration
- `/payments/restore` - Restores previous purchases
- Validates historical transactions
- Updates user balance and subscriptions

### Balance Management
- `/balance` - Returns current user balance
- Includes subscription and pack days
- Shows current plan and status

## Configuration

### Test Timing
- Default delay between major test steps: 20 seconds
- Store transaction simulation: 2 seconds
- API call simulation: 1 second
- Multiple purchase delays: 5 seconds

### Mock Data
All test data is self-contained and doesn't require external dependencies. The mock backend routes provide realistic responses that match the actual API structure.

## Integration with CI/CD

The test suite is designed to be integrated into continuous integration pipelines:

```yaml
# Example GitHub Actions step
- name: Run Payment Tests
  run: bun run tests/payment-scenarios.ts
```

## Troubleshooting

### Common Issues
1. **Bun not found**: Ensure Bun runtime is installed
2. **Test timeouts**: Adjust delay values if needed
3. **Mock data issues**: Verify mock backend routes match actual API

### Debug Mode
Add additional logging by modifying the `log` method in `PaymentTestRunner` class.

## Contributing

When adding new test scenarios:
1. Follow the existing test pattern
2. Include comprehensive logging
3. Add appropriate delays between steps
4. Update this README with new test descriptions

## Related Files
- `mobile/lib/hooks/usePayments.ts` - Main payment hook
- `mobile/app/buy-credits.tsx` - Purchase UI
- `backend/src/payments/` - Backend payment services
