#!/usr/bin/env node
import 'source-map-support/register';
import * as cdk from 'aws-cdk-lib';
import { iTripBackendStack } from '../lib/itrip-backend-stack';
import { iTripSharedStack } from '../lib/itrip-shared-stack';

const app = new cdk.App();

// Environment configuration
const env = {
  account: process.env.CDK_DEFAULT_ACCOUNT,
  region: process.env.CDK_DEFAULT_REGION || 'us-east-1',
};

// Shared resources (ECR repository, etc.)
const sharedStack = new iTripSharedStack(app, 'iTripSharedStack', {
  env,
  tags: {
    Project: 'iTrip',
    ManagedBy: 'CDK',
    StackType: 'Shared',
  },
});

// Staging environment
const stagingStack = new iTripBackendStack(app, 'iTripStagingStack', {
  env,
  environment: 'staging',
  domainName: 'staging-api.itrip.com', // Replace with your domain
  certificateArn: process.env.STAGING_CERTIFICATE_ARN,
  ecrRepository: sharedStack.ecrRepository,
  tags: {
    Environment: 'staging',
    Project: 'iTrip',
    ManagedBy: 'CDK',
  },
});

// Production environment
const productionStack = new iTripBackendStack(app, 'iTripProductionStack', {
  env,
  environment: 'production',
  domainName: 'api.itrip.com', // Replace with your domain
  certificateArn: process.env.PRODUCTION_CERTIFICATE_ARN,
  ecrRepository: sharedStack.ecrRepository,
  tags: {
    Environment: 'production',
    Project: 'iTrip',
    ManagedBy: 'CDK',
  },
});

// Add dependencies
stagingStack.addDependency(sharedStack);
productionStack.addDependency(sharedStack);
