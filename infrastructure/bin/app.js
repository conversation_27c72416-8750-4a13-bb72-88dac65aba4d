#!/usr/bin/env node
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
require("source-map-support/register");
const cdk = require("aws-cdk-lib");
const itrip_backend_stack_1 = require("../lib/itrip-backend-stack");
const itrip_shared_stack_1 = require("../lib/itrip-shared-stack");
const app = new cdk.App();
// Environment configuration
const env = {
    account: process.env.CDK_DEFAULT_ACCOUNT,
    region: process.env.CDK_DEFAULT_REGION || 'us-east-1',
};
// Shared resources (ECR repository, etc.)
const sharedStack = new itrip_shared_stack_1.iTripSharedStack(app, 'iTripSharedStack', {
    env,
    tags: {
        Project: 'iTrip',
        ManagedBy: 'CDK',
        StackType: 'Shared',
    },
});
// Staging environment
const stagingStack = new itrip_backend_stack_1.iTripBackendStack(app, 'iTripStagingStack', {
    env,
    environment: 'staging',
    domainName: 'staging-api.itrip.com',
    certificateArn: process.env.STAGING_CERTIFICATE_ARN,
    ecrRepository: sharedStack.ecrRepository,
    tags: {
        Environment: 'staging',
        Project: 'iTrip',
        ManagedBy: 'CDK',
    },
});
// Production environment
const productionStack = new itrip_backend_stack_1.iTripBackendStack(app, 'iTripProductionStack', {
    env,
    environment: 'production',
    domainName: 'api.itrip.com',
    certificateArn: process.env.PRODUCTION_CERTIFICATE_ARN,
    ecrRepository: sharedStack.ecrRepository,
    tags: {
        Environment: 'production',
        Project: 'iTrip',
        ManagedBy: 'CDK',
    },
});
// Add dependencies
stagingStack.addDependency(sharedStack);
productionStack.addDependency(sharedStack);
//# sourceMappingURL=data:application/json;base64,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