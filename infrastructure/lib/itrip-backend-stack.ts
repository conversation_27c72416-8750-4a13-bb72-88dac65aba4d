import * as cdk from 'aws-cdk-lib';
import * as ec2 from 'aws-cdk-lib/aws-ec2';
import * as ecs from 'aws-cdk-lib/aws-ecs';
import * as ecr from 'aws-cdk-lib/aws-ecr';
import * as elbv2 from 'aws-cdk-lib/aws-elasticloadbalancingv2';
import * as logs from 'aws-cdk-lib/aws-logs';
import * as s3 from 'aws-cdk-lib/aws-s3';
import * as iam from 'aws-cdk-lib/aws-iam';
import * as certificateManager from 'aws-cdk-lib/aws-certificatemanager';
import { Construct } from 'constructs';

export interface iTripBackendStackProps extends cdk.StackProps {
  environment: string;
  domainName?: string;
  certificateArn?: string;
  ecrRepository?: ecr.IRepository;
}

export class iTripBackendStack extends cdk.Stack {
  constructor(scope: Construct, id: string, props: iTripBackendStackProps) {
    super(scope, id, props);

    const { environment } = props;

    // VPC
    const vpc = new ec2.Vpc(this, 'VPC', {
      maxAzs: 2,
      natGateways: 1,
      subnetConfiguration: [
        {
          cidrMask: 24,
          name: 'public',
          subnetType: ec2.SubnetType.PUBLIC,
        },
        {
          cidrMask: 24,
          name: 'private',
          subnetType: ec2.SubnetType.PRIVATE_WITH_EGRESS,
        },
      ],
    });

    // ECR Repository (use provided repository or reference existing one)
    const repository = props.ecrRepository ||
      ecr.Repository.fromRepositoryName(this, 'Repository', 'itrip-backend');

    // S3 Bucket for Environment Files
    const configBucket = new s3.Bucket(this, 'ConfigBucket', {
      bucketName: `itrip-config-${environment}-${this.account}`,
      versioned: true,
      encryption: s3.BucketEncryption.S3_MANAGED,
      blockPublicAccess: s3.BlockPublicAccess.BLOCK_ALL,
      removalPolicy: cdk.RemovalPolicy.RETAIN,
    });

    // ECS Cluster
    const cluster = new ecs.Cluster(this, 'Cluster', {
      vpc,
      clusterName: `itrip-${environment}`,
      containerInsights: true,
    });

    // Application Load Balancer
    const alb = new elbv2.ApplicationLoadBalancer(this, 'ALB', {
      vpc,
      internetFacing: true,
      loadBalancerName: `itrip-alb-${environment}`,
    });

    // Security Group for ALB
    const albSecurityGroup = new ec2.SecurityGroup(this, 'ALBSecurityGroup', {
      vpc,
      description: 'Security group for ALB',
      allowAllOutbound: true,
    });

    albSecurityGroup.addIngressRule(
      ec2.Peer.anyIpv4(),
      ec2.Port.tcp(80),
      'Allow HTTP traffic'
    );

    albSecurityGroup.addIngressRule(
      ec2.Peer.anyIpv4(),
      ec2.Port.tcp(443),
      'Allow HTTPS traffic'
    );

    alb.addSecurityGroup(albSecurityGroup);

    // Security Group for ECS Service
    const ecsSecurityGroup = new ec2.SecurityGroup(this, 'ECSSecurityGroup', {
      vpc,
      description: 'Security group for ECS service',
      allowAllOutbound: true,
    });

    ecsSecurityGroup.addIngressRule(
      albSecurityGroup,
      ec2.Port.tcp(3000),
      'Allow traffic from ALB'
    );

    // Task Role for accessing AWS services
    const taskRole = new iam.Role(this, 'TaskRole', {
      assumedBy: new iam.ServicePrincipal('ecs-tasks.amazonaws.com'),
      description: `Task role for iTrip backend ${environment} environment`,
    });

    // Grant access to S3 Config Bucket
    configBucket.grantRead(taskRole);

    // Execution Role for ECS tasks (for pulling images, logging, etc.)
    const executionRole = new iam.Role(this, 'ExecutionRole', {
      assumedBy: new iam.ServicePrincipal('ecs-tasks.amazonaws.com'),
      managedPolicies: [
        iam.ManagedPolicy.fromAwsManagedPolicyName('service-role/AmazonECSTaskExecutionRolePolicy'),
      ],
      description: `Execution role for iTrip backend ${environment} environment`,
    });

    // Grant execution role access to S3 Config Bucket for environment files
    configBucket.grantRead(executionRole);

    // Task Definition
    const taskDefinition = new ecs.FargateTaskDefinition(this, 'TaskDefinition', {
      memoryLimitMiB: 1024,
      cpu: 512,
      family: `itrip-backend-${environment}`,
      taskRole: taskRole,
      executionRole: executionRole,
    });

    // CloudWatch Log Group
    const skipExistingParameters = this.node.tryGetContext('skipExistingParameters') === 'true';
    let logGroup: logs.ILogGroup;

    if (skipExistingParameters) {
      // Reference existing log group if it exists
      try {
        logGroup = logs.LogGroup.fromLogGroupName(this, 'ExistingLogGroup', `/ecs/itrip-backend-${environment}`);
      } catch (error) {
        // If referencing fails, create new log group
        logGroup = new logs.LogGroup(this, 'LogGroup', {
          logGroupName: `/ecs/itrip-backend-${environment}`,
          retention: logs.RetentionDays.ONE_WEEK,
          removalPolicy: cdk.RemovalPolicy.DESTROY,
        });
      }
    } else {
      // Create new log group
      logGroup = new logs.LogGroup(this, 'LogGroup', {
        logGroupName: `/ecs/itrip-backend-${environment}`,
        retention: logs.RetentionDays.ONE_WEEK,
        removalPolicy: cdk.RemovalPolicy.DESTROY,
      });
    }

    // Container Definition (name must match GitHub Actions workflow)
    const container = taskDefinition.addContainer('itrip-backend', {
      image: ecs.ContainerImage.fromEcrRepository(repository, 'latest'),
      environmentFiles: [
        ecs.EnvironmentFile.fromBucket(
          configBucket,
          `environments/${environment}/.env`
        ),
      ],
      logging: ecs.LogDrivers.awsLogs({
        streamPrefix: 'ecs',
        logGroup,
      }),
    });

    container.addPortMappings({
      containerPort: 3000,
      protocol: ecs.Protocol.TCP,
    });

    // ECS Service
    const service = new ecs.FargateService(this, 'Service', {
      cluster,
      taskDefinition,
      serviceName: `itrip-backend-${environment}`,
      desiredCount: environment === 'production' ? 2 : 1,
      securityGroups: [ecsSecurityGroup],
      assignPublicIp: false,
      enableExecuteCommand: true,
    });

    // Target Group
    const targetGroup = new elbv2.ApplicationTargetGroup(this, 'TargetGroup', {
      vpc,
      port: 3000,
      protocol: elbv2.ApplicationProtocol.HTTP,
      targetType: elbv2.TargetType.IP,
      healthCheck: {
        enabled: true,
        path: '/health',
        healthyHttpCodes: '200',
        interval: cdk.Duration.seconds(30),
        timeout: cdk.Duration.seconds(5),
        healthyThresholdCount: 2,
        unhealthyThresholdCount: 3,
      },
    });

    service.attachToApplicationTargetGroup(targetGroup);

    // ALB Listener
    const listener = alb.addListener('Listener', {
      port: 80,
      protocol: elbv2.ApplicationProtocol.HTTP,
      defaultTargetGroups: [targetGroup],
    });

    // SSL Certificate and HTTPS Listener (if domain is provided)
    if (props.domainName && props.certificateArn) {
      const certificate = certificateManager.Certificate.fromCertificateArn(
        this,
        'Certificate',
        props.certificateArn
      );

      alb.addListener('HTTPSListener', {
        port: 443,
        protocol: elbv2.ApplicationProtocol.HTTPS,
        certificates: [certificate],
        defaultTargetGroups: [targetGroup],
      });

      // Redirect HTTP to HTTPS
      listener.addAction('HTTPSRedirect', {
        action: elbv2.ListenerAction.redirect({
          protocol: 'HTTPS',
          port: '443',
          permanent: true,
        }),
      });
    }

    // Outputs
    new cdk.CfnOutput(this, 'LoadBalancerDNS', {
      value: alb.loadBalancerDnsName,
      description: 'DNS name of the load balancer',
    });

    new cdk.CfnOutput(this, 'ECRRepositoryURI', {
      value: repository.repositoryUri,
      description: 'ECR Repository URI',
    });

    new cdk.CfnOutput(this, 'ClusterName', {
      value: cluster.clusterName,
      description: 'ECS Cluster Name',
    });

    new cdk.CfnOutput(this, 'ServiceName', {
      value: service.serviceName,
      description: 'ECS Service Name',
    });

    new cdk.CfnOutput(this, 'TaskDefinitionArn', {
      value: taskDefinition.taskDefinitionArn,
      description: 'Task Definition ARN',
    });

    new cdk.CfnOutput(this, 'ConfigBucketName', {
      value: configBucket.bucketName,
      description: 'S3 Bucket for Environment Configuration',
    });

    new cdk.CfnOutput(this, 'EnvironmentFilePath', {
      value: `s3://${configBucket.bucketName}/environments/${environment}/.env`,
      description: 'S3 path for environment file',
    });
  }
}
