import * as cdk from 'aws-cdk-lib';
import * as ecr from 'aws-cdk-lib/aws-ecr';
import { Construct } from 'constructs';
export interface iTripBackendStackProps extends cdk.StackProps {
    environment: string;
    domainName?: string;
    certificateArn?: string;
    ecrRepository?: ecr.IRepository;
}
export declare class iTripBackendStack extends cdk.Stack {
    constructor(scope: Construct, id: string, props: iTripBackendStackProps);
}
