"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.iTripSharedStack = void 0;
const cdk = require("aws-cdk-lib");
const ecr = require("aws-cdk-lib/aws-ecr");
/**
 * Shared resources stack for iTrip
 * Contains resources that are shared across environments (staging and production)
 */
class iTripSharedStack extends cdk.Stack {
    constructor(scope, id, props) {
        super(scope, id, props);
        // ECR Repository (shared across all environments)
        // Check if we should create a new repository or reference an existing one
        const repositoryName = 'itrip-backend';
        const useExistingRepository = this.node.tryGetContext('useExistingECR') === 'true';
        if (useExistingRepository) {
            // Reference existing ECR repository
            this.ecrRepository = ecr.Repository.fromRepositoryName(this, 'ExistingRepository', repositoryName);
        }
        else {
            // Create new ECR repository
            this.ecrRepository = new ecr.Repository(this, 'Repository', {
                repositoryName: repositoryName,
                removalPolicy: cdk.RemovalPolicy.RETAIN,
                lifecycleRules: [
                    {
                        maxImageCount: 10,
                        tagStatus: ecr.TagStatus.UNTAGGED,
                        description: 'Keep only 10 untagged images',
                    },
                    {
                        maxImageCount: 20,
                        tagStatus: ecr.TagStatus.TAGGED,
                        tagPrefixList: ['v', 'latest', 'staging', 'production'],
                        description: 'Keep only 20 tagged images with specified prefixes',
                    },
                ],
                imageScanOnPush: true,
            });
        }
        // Outputs
        new cdk.CfnOutput(this, 'ECRRepositoryURI', {
            value: this.ecrRepository.repositoryUri,
            description: 'ECR Repository URI',
            exportName: 'iTripECRRepositoryURI',
        });
        new cdk.CfnOutput(this, 'ECRRepositoryName', {
            value: this.ecrRepository.repositoryName,
            description: 'ECR Repository Name',
            exportName: 'iTripECRRepositoryName',
        });
    }
}
exports.iTripSharedStack = iTripSharedStack;
//# sourceMappingURL=data:application/json;base64,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