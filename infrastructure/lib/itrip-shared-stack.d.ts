import * as cdk from 'aws-cdk-lib';
import * as ecr from 'aws-cdk-lib/aws-ecr';
import { Construct } from 'constructs';
/**
 * Shared resources stack for iTrip
 * Contains resources that are shared across environments (staging and production)
 */
export declare class iTripSharedStack extends cdk.Stack {
    readonly ecrRepository: ecr.IRepository;
    constructor(scope: Construct, id: string, props?: cdk.StackProps);
}
