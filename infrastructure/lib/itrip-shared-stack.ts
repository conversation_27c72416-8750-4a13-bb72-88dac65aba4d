import * as cdk from 'aws-cdk-lib';
import * as ecr from 'aws-cdk-lib/aws-ecr';
import { Construct } from 'constructs';

/**
 * Shared resources stack for iTrip
 * Contains resources that are shared across environments (staging and production)
 */
export class iTripSharedStack extends cdk.Stack {
  public readonly ecrRepository: ecr.IRepository;

  constructor(scope: Construct, id: string, props?: cdk.StackProps) {
    super(scope, id, props);

    // ECR Repository (shared across all environments)
    // Check if we should create a new repository or reference an existing one
    const repositoryName = 'itrip-backend';
    const useExistingRepository = this.node.tryGetContext('useExistingECR') === 'true';

    if (useExistingRepository) {
      // Reference existing ECR repository
      this.ecrRepository = ecr.Repository.fromRepositoryName(
        this,
        'ExistingRepository',
        repositoryName
      );
    } else {
      // Create new ECR repository
      this.ecrRepository = new ecr.Repository(this, 'Repository', {
        repositoryName: repositoryName,
        removalPolicy: cdk.RemovalPolicy.RETAIN,
        lifecycleRules: [
          {
            maxImageCount: 10,
            tagStatus: ecr.TagStatus.UNTAGGED,
            description: 'Keep only 10 untagged images',
          },
          {
            maxImageCount: 20,
            tagStatus: ecr.TagStatus.TAGGED,
            tagPrefixList: ['v', 'latest', 'staging', 'production'],
            description: 'Keep only 20 tagged images with specified prefixes',
          },
        ],
        imageScanOnPush: true,
      });
    }

    // Outputs
    new cdk.CfnOutput(this, 'ECRRepositoryURI', {
      value: this.ecrRepository.repositoryUri,
      description: 'ECR Repository URI',
      exportName: 'iTripECRRepositoryURI',
    });

    new cdk.CfnOutput(this, 'ECRRepositoryName', {
      value: this.ecrRepository.repositoryName,
      description: 'ECR Repository Name',
      exportName: 'iTripECRRepositoryName',
    });
  }
}
