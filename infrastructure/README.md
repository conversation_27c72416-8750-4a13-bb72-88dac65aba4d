# iTrip Infrastructure

This directory contains the AWS CDK infrastructure code for the iTrip backend application.

## Architecture Overview

The infrastructure consists of three main stacks:

1. **iTripSharedStack** - Shared resources across environments
   - ECR Repository for Docker images
   - Shared networking components

2. **iTripStagingStack** - Staging environment
   - ECS Cluster: `itrip-staging`
   - ECS Service: `itrip-backend-staging`
   - Application Load Balancer
   - Parameter Store configuration

3. **iTripProductionStack** - Production environment
   - ECS Cluster: `itrip-production`
   - ECS Service: `itrip-backend-production`
   - Application Load Balancer
   - Parameter Store configuration

## Prerequisites

1. **AWS CLI** configured with appropriate credentials
2. **Node.js** (version 18 or later)
3. **AWS CDK** installed globally: `npm install -g aws-cdk`
4. **Docker** for building images

## Handling Existing Resources

This infrastructure is designed to handle existing AWS resources gracefully. If you have existing resources (like ECR repositories), the deployment will detect and use them instead of creating new ones.

### ECR Repository

If an ECR repository named `itrip-backend` already exists, the infrastructure will reference it instead of creating a new one. This is controlled by the `useExistingECR` context variable in `cdk.json`.

## Quick Start

### 1. Deploy Infrastructure (Recommended)

Use the provided deployment script that handles existing resources automatically:

```bash
# Deploy all stacks with automatic resource detection
./scripts/deploy.sh

# Deploy specific stack
./scripts/deploy.sh iTripSharedStack
./scripts/deploy.sh iTripStagingStack
./scripts/deploy.sh iTripProductionStack
```

### Alternative: Manual Deployment

```bash
# Deploy all stacks (recommended for first-time setup)
../scripts/deploy-infrastructure.sh --environment all

# Or deploy specific environments
../scripts/deploy-infrastructure.sh --environment staging
../scripts/deploy-infrastructure.sh --environment production
```

### 2. Update Parameter Store Values

```bash
# List current parameters
../scripts/update-parameters.sh -e staging -l

# Update specific parameters
../scripts/update-parameters.sh -e staging -p MONGODB_URI -v "mongodb://your-connection-string"
../scripts/update-parameters.sh -e staging -p JWT_SECRET -v "your-jwt-secret"
```

### 3. Build and Deploy Application

```bash
# Build and push Docker image
../scripts/build-and-push.sh latest

# The script will offer to update ECS services automatically
```

## Manual Deployment Commands

### Using NPM Scripts

```bash
# Install dependencies
npm install

# Build TypeScript
npm run build

# Deploy individual stacks
npm run deploy:shared
npm run deploy:staging
npm run deploy:production

# Deploy all stacks
npm run deploy:all

# View differences before deployment
npm run diff:staging
npm run diff:production

# List all stacks
npm run list
```

### Using CDK Directly

```bash
# Bootstrap CDK (one-time setup)
npx cdk bootstrap

# Deploy stacks
npx cdk deploy iTripSharedStack
npx cdk deploy iTripStagingStack
npx cdk deploy iTripProductionStack

# Destroy stacks (be careful!)
npx cdk destroy iTripStagingStack
npx cdk destroy iTripProductionStack
npx cdk destroy iTripSharedStack
```

## Environment Variables

The following parameters are created in AWS Parameter Store for each environment:

- `/itrip/{environment}/MONGODB_URI`
- `/itrip/{environment}/JWT_SECRET`
- `/itrip/{environment}/JWT_EXPIRES_IN`
- `/itrip/{environment}/OPENAI_API_KEY`
- `/itrip/{environment}/OPENAI_API_KEY_BACKUP`
- `/itrip/{environment}/GROQ_API_KEY`
- `/itrip/{environment}/GROQ_API_KEY_BACKUP`
- `/itrip/{environment}/DEEPSEEK_API_KEY`
- `/itrip/{environment}/DEEPSEEK_API_KEY_BACKUP`
- `/itrip/{environment}/GOOGLE_MAPS_API_KEY`
- `/itrip/{environment}/ORS_API_KEY`

**Important**: Update these placeholder values with actual secrets after deployment.

## Monitoring and Troubleshooting

### Check ECS Service Status

```bash
# Staging
aws ecs describe-services --cluster itrip-staging --services itrip-backend-staging

# Production
aws ecs describe-services --cluster itrip-production --services itrip-backend-production
```

### View Logs

```bash
# Staging logs
aws logs tail /ecs/itrip-backend-staging --follow

# Production logs
aws logs tail /ecs/itrip-backend-production --follow
```

### Force Service Restart

```bash
# Staging
aws ecs update-service --cluster itrip-staging --service itrip-backend-staging --force-new-deployment

# Production
aws ecs update-service --cluster itrip-production --service itrip-backend-production --force-new-deployment
```

## Security Considerations

1. **Parameter Store**: All sensitive values are stored in AWS Parameter Store
2. **VPC**: Services run in private subnets with NAT Gateway for outbound access
3. **Security Groups**: Restrictive security groups limit access between components
4. **IAM Roles**: Least privilege access for ECS tasks
5. **ALB**: Application Load Balancer handles SSL termination

## Cost Optimization

- **Staging**: Single ECS task, smaller instance sizes
- **Production**: Multiple ECS tasks for high availability
- **ECR**: Lifecycle policies to clean up old images
- **Logs**: 1-week retention for cost control

## Troubleshooting Common Issues

### Resource Already Exists Error

If you encounter "Resource already exists" errors during deployment:

1. **Use the deployment script** (recommended):
   ```bash
   ./scripts/deploy.sh
   ```
   This automatically detects existing resources and configures CDK accordingly.

2. **Clean up failed stacks**:
   ```bash
   ./scripts/cleanup.sh cleanup-failed
   ```

3. **Check existing resources**:
   ```bash
   # List existing stacks
   ./scripts/cleanup.sh list

   # Show stack events for debugging
   ./scripts/cleanup.sh events iTripSharedStack
   ```

4. **Manual context configuration**:
   ```bash
   # If ECR repository already exists
   npx cdk deploy --context useExistingECR=true

   # If creating new resources
   npx cdk deploy --context useExistingECR=false
   ```

### ECS Service Missing Error

If you get "service is MISSING" error in GitHub Actions:
1. Ensure the CDK stacks are deployed
2. Check that the service names match in GitHub Actions workflow
3. Verify the cluster exists and is in the correct region

### Parameter Store Access Issues

1. Ensure ECS task role has Parameter Store permissions
2. Check parameter paths match the expected format
3. Verify parameters exist in the correct region

### Docker Image Issues

1. Ensure ECR repository exists and is accessible
2. Check that the image tag matches what's expected
3. Verify Docker image builds successfully locally

## Cleanup and Management Scripts

### Deployment Script (`scripts/deploy.sh`)

Features:
- Automatic detection of existing ECR repositories
- CDK bootstrapping if needed
- Context configuration based on existing resources
- Error handling and helpful messages

### Cleanup Script (`scripts/cleanup.sh`)

Manage CloudFormation stacks:

```bash
# List all iTrip stacks
./scripts/cleanup.sh list

# Delete specific stack
./scripts/cleanup.sh delete iTripSharedStack

# Delete all stacks
./scripts/cleanup.sh delete-all

# Clean up failed stacks
./scripts/cleanup.sh cleanup-failed

# Show stack events for debugging
./scripts/cleanup.sh events iTripSharedStack
```
