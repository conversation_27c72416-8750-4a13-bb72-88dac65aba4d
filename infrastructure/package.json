{"name": "itrip-infrastructure", "version": "1.0.0", "description": "AWS CDK infrastructure for iTrip backend", "main": "lib/app.js", "scripts": {"build": "tsc", "watch": "tsc -w", "test": "jest", "cdk": "cdk", "deploy:shared": "cdk deploy iTripSharedStack --require-approval never", "deploy:staging": "cdk deploy iTripStagingStack --require-approval never", "deploy:production": "cdk deploy iTripProductionStack --require-approval never", "deploy:all": "cdk deploy iTripSharedStack iTripStagingStack iTripProductionStack --require-approval never", "destroy:shared": "cdk destroy iTripSharedStack", "destroy:staging": "cdk destroy iTripStagingStack", "destroy:production": "cdk destroy iTripProductionStack", "destroy:all": "cdk destroy iTripProductionStack iTripStagingStack iTripSharedStack", "diff:shared": "cdk diff iTripSharedStack", "diff:staging": "cdk diff iTripStagingStack", "diff:production": "cdk diff iTripProductionStack", "synth": "cdk synth", "list": "cdk list"}, "devDependencies": {"@types/jest": "^29.5.5", "@types/node": "20.6.0", "jest": "^29.7.0", "ts-jest": "^29.1.1", "typescript": "~5.2.2"}, "dependencies": {"aws-cdk-lib": "2.100.0", "constructs": "^10.0.0"}}