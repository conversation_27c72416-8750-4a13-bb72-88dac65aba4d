#!/bin/bash

# iTrip Infrastructure Deployment Script
# This script handles deployment with existing resources

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if AWS CLI is configured
check_aws_cli() {
    if ! command -v aws &> /dev/null; then
        print_error "AWS CLI is not installed. Please install it first."
        exit 1
    fi

    if ! aws sts get-caller-identity &> /dev/null; then
        print_error "AWS CLI is not configured. Please run 'aws configure' first."
        exit 1
    fi

    print_success "AWS CLI is configured"
}

# Function to check if ECR repository exists
check_ecr_repository() {
    local repo_name="itrip-backend"
    print_status "Checking if ECR repository '$repo_name' exists..."

    if aws ecr describe-repositories --repository-names "$repo_name" &> /dev/null; then
        print_warning "ECR repository '$repo_name' already exists. Will use existing repository."
        return 0
    else
        print_status "ECR repository '$repo_name' does not exist. Will create new repository."
        return 1
    fi
}

# Function to check if ECS clusters exist
check_ecs_clusters() {
    print_status "Checking for existing ECS clusters..."

    local staging_cluster="itrip-staging"
    local production_cluster="itrip-production"
    local existing_clusters=()

    # Check staging cluster
    if aws ecs describe-clusters --clusters "$staging_cluster" --query 'clusters[0].status' --output text 2>/dev/null | grep -q "ACTIVE"; then
        print_warning "ECS cluster '$staging_cluster' already exists."
        existing_clusters+=("staging")
    fi

    # Check production cluster
    if aws ecs describe-clusters --clusters "$production_cluster" --query 'clusters[0].status' --output text 2>/dev/null | grep -q "ACTIVE"; then
        print_warning "ECS cluster '$production_cluster' already exists."
        existing_clusters+=("production")
    fi

    if [ ${#existing_clusters[@]} -gt 0 ]; then
        print_warning "Found existing ECS clusters: ${existing_clusters[*]}"
        return 0
    else
        print_status "No existing ECS clusters found."
        return 1
    fi
}

# Function to check if Parameter Store parameters exist
check_parameter_store() {
    local environment=$1
    print_status "Checking for existing Parameter Store parameters for environment '$environment'..."

    local parameter_prefix="/itrip/$environment/"
    local existing_params=$(aws ssm get-parameters-by-path --path "$parameter_prefix" --query 'Parameters[].Name' --output text 2>/dev/null || echo "")

    if [ -n "$existing_params" ]; then
        print_warning "Found existing Parameter Store parameters for environment '$environment':"
        echo "$existing_params" | tr '\t' '\n' | sed 's/^/  - /'
        return 0
    else
        print_status "No existing Parameter Store parameters found for environment '$environment'."
        return 1
    fi
}

# Function to check if Load Balancers exist
check_load_balancers() {
    print_status "Checking for existing Application Load Balancers..."

    local staging_alb="itrip-alb-staging"
    local production_alb="itrip-alb-production"
    local existing_albs=()

    # Check staging ALB
    if aws elbv2 describe-load-balancers --names "$staging_alb" --query 'LoadBalancers[0].State.Code' --output text 2>/dev/null | grep -q "active"; then
        print_warning "Load Balancer '$staging_alb' already exists."
        existing_albs+=("staging")
    fi

    # Check production ALB
    if aws elbv2 describe-load-balancers --names "$production_alb" --query 'LoadBalancers[0].State.Code' --output text 2>/dev/null | grep -q "active"; then
        print_warning "Load Balancer '$production_alb' already exists."
        existing_albs+=("production")
    fi

    if [ ${#existing_albs[@]} -gt 0 ]; then
        print_warning "Found existing Load Balancers: ${existing_albs[*]}"
        return 0
    else
        print_status "No existing Load Balancers found."
        return 1
    fi
}

# Function to update CDK context based on existing resources
update_cdk_context() {
    local use_existing_ecr=$1
    local skip_existing_parameters=$2

    print_status "Updating CDK context..."

    # Update cdk.json with appropriate context
    if [ "$use_existing_ecr" = true ]; then
        # Set useExistingECR to true
        jq '.context.useExistingECR = "true"' cdk.json > cdk.json.tmp && mv cdk.json.tmp cdk.json
        print_success "Set useExistingECR to true"
    else
        # Set useExistingECR to false
        jq '.context.useExistingECR = "false"' cdk.json > cdk.json.tmp && mv cdk.json.tmp cdk.json
        print_success "Set useExistingECR to false"
    fi

    if [ "$skip_existing_parameters" = true ]; then
        # Set skipExistingParameters to true
        jq '.context.skipExistingParameters = "true"' cdk.json > cdk.json.tmp && mv cdk.json.tmp cdk.json
        print_success "Set skipExistingParameters to true"
    else
        # Set skipExistingParameters to false
        jq '.context.skipExistingParameters = "false"' cdk.json > cdk.json.tmp && mv cdk.json.tmp cdk.json
        print_success "Set skipExistingParameters to false"
    fi
}

# Function to bootstrap CDK if needed
bootstrap_cdk() {
    print_status "Checking if CDK is bootstrapped..."

    local account=$(aws sts get-caller-identity --query Account --output text)
    local region=$(aws configure get region || echo "us-east-1")

    if ! aws cloudformation describe-stacks --stack-name CDKToolkit --region "$region" &> /dev/null; then
        print_status "Bootstrapping CDK..."
        npx cdk bootstrap "aws://$account/$region"
        print_success "CDK bootstrapped successfully"
    else
        print_success "CDK is already bootstrapped"
    fi
}

# Function to deploy stacks
deploy_stacks() {
    local stack_name=$1

    if [ -z "$stack_name" ]; then
        print_status "Deploying all stacks..."
        npx cdk deploy --all --require-approval never
    else
        print_status "Deploying stack: $stack_name"
        npx cdk deploy "$stack_name" --require-approval never
    fi
}

# Function to handle deployment errors
handle_deployment_error() {
    local exit_code=$1

    if [ $exit_code -ne 0 ]; then
        print_error "Deployment failed with exit code $exit_code"
        print_warning "Common solutions:"
        echo "  1. Check if resources already exist and update context accordingly"
        echo "  2. Verify AWS credentials and permissions"
        echo "  3. Check CloudFormation console for detailed error messages"
        echo "  4. Try deploying individual stacks: ./deploy.sh <stack-name>"
        exit $exit_code
    fi
}

# Main deployment function
main() {
    local stack_name=$1

    print_status "Starting iTrip infrastructure deployment..."

    # Check prerequisites
    check_aws_cli

    # Check for existing resources
    local use_existing_ecr=false
    local skip_existing_parameters=false

    if check_ecr_repository; then
        use_existing_ecr=true
    fi

    # Check for existing ECS clusters (don't fail on return code)
    check_ecs_clusters || true

    # Check for existing Load Balancers (don't fail on return code)
    check_load_balancers || true

    # Check for existing Parameter Store parameters based on stack being deployed
    if [ -z "$stack_name" ] || [[ "$stack_name" == *"Staging"* ]]; then
        if check_parameter_store "staging"; then
            skip_existing_parameters=true
        fi
    fi

    if [ -z "$stack_name" ] || [[ "$stack_name" == *"Production"* ]]; then
        if check_parameter_store "production"; then
            skip_existing_parameters=true
        fi
    fi

    # Update CDK context based on findings
    update_cdk_context "$use_existing_ecr" "$skip_existing_parameters"

    # Show summary of existing resources
    if [ "$use_existing_ecr" = true ] || [ "$skip_existing_parameters" = true ]; then
        print_warning "Summary of existing resources detected:"
        [ "$use_existing_ecr" = true ] && echo "  - ECR repository: Will use existing 'itrip-backend'"
        [ "$skip_existing_parameters" = true ] && echo "  - Parameter Store: Will handle existing parameters gracefully"
        echo ""
        print_status "Deployment will proceed with existing resource handling enabled."
        echo ""
    fi

    # Bootstrap CDK if needed
    bootstrap_cdk

    # Install dependencies
    print_status "Installing dependencies..."
    npm install

    # Deploy stacks
    deploy_stacks "$stack_name"
    handle_deployment_error $?

    print_success "Deployment completed successfully!"
    print_status "Next steps:"
    echo "  1. Update Parameter Store values with actual secrets"
    echo "  2. Configure your domain and SSL certificates"
    echo "  3. Update GitHub Actions secrets with deployment outputs"
}

# Help function
show_help() {
    echo "Usage: $0 [stack-name]"
    echo ""
    echo "Deploy iTrip infrastructure to AWS"
    echo ""
    echo "Arguments:"
    echo "  stack-name    Optional. Deploy specific stack (iTripSharedStack, iTripStagingStack, iTripProductionStack)"
    echo ""
    echo "Examples:"
    echo "  $0                           # Deploy all stacks"
    echo "  $0 iTripSharedStack         # Deploy only shared stack"
    echo "  $0 iTripStagingStack        # Deploy only staging stack"
    echo ""
    echo "Environment Variables:"
    echo "  STAGING_CERTIFICATE_ARN     SSL certificate ARN for staging"
    echo "  PRODUCTION_CERTIFICATE_ARN  SSL certificate ARN for production"
}

# Parse command line arguments
case "${1:-}" in
    -h|--help)
        show_help
        exit 0
        ;;
    *)
        main "$1"
        ;;
esac
