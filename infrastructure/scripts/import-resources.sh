#!/bin/bash

# iTrip Resource Import Script
# This script helps import existing AWS resources into CDK management

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if AWS CLI is configured
check_aws_cli() {
    if ! command -v aws &> /dev/null; then
        print_error "AWS CLI is not installed. Please install it first."
        exit 1
    fi

    if ! aws sts get-caller-identity &> /dev/null; then
        print_error "AWS CLI is not configured. Please run 'aws configure' first."
        exit 1
    fi

    print_success "AWS CLI is configured"
}

# Function to list existing resources that can be imported
list_importable_resources() {
    print_status "Scanning for existing AWS resources that can be imported..."
    
    echo ""
    print_status "=== ECR Repositories ==="
    aws ecr describe-repositories --query 'repositories[?contains(repositoryName, `itrip`)].{Name:repositoryName,URI:repositoryUri,Created:createdAt}' --output table 2>/dev/null || echo "No ECR repositories found"
    
    echo ""
    print_status "=== ECS Clusters ==="
    aws ecs list-clusters --query 'clusterArns[?contains(@, `itrip`)]' --output table 2>/dev/null || echo "No ECS clusters found"
    
    echo ""
    print_status "=== Load Balancers ==="
    aws elbv2 describe-load-balancers --query 'LoadBalancers[?contains(LoadBalancerName, `itrip`)].{Name:LoadBalancerName,DNS:DNSName,State:State.Code}' --output table 2>/dev/null || echo "No Load Balancers found"
    
    echo ""
    print_status "=== Parameter Store Parameters ==="
    aws ssm get-parameters-by-path --path "/itrip/" --recursive --query 'Parameters[].{Name:Name,Type:Type,LastModified:LastModifiedDate}' --output table 2>/dev/null || echo "No Parameter Store parameters found"
    
    echo ""
    print_status "=== VPCs ==="
    aws ec2 describe-vpcs --filters "Name=tag:Name,Values=*itrip*" --query 'Vpcs[].{VpcId:VpcId,CidrBlock:CidrBlock,State:State,Name:Tags[?Key==`Name`].Value|[0]}' --output table 2>/dev/null || echo "No tagged VPCs found"
    
    echo ""
    print_status "=== Security Groups ==="
    aws ec2 describe-security-groups --filters "Name=group-name,Values=*itrip*" --query 'SecurityGroups[].{GroupId:GroupId,GroupName:GroupName,Description:Description}' --output table 2>/dev/null || echo "No Security Groups found"
}

# Function to generate import commands for ECR repository
generate_ecr_import() {
    local repo_name="itrip-backend"
    
    if aws ecr describe-repositories --repository-names "$repo_name" &> /dev/null; then
        print_status "Generating import command for ECR repository '$repo_name'..."
        
        local repo_arn=$(aws ecr describe-repositories --repository-names "$repo_name" --query 'repositories[0].repositoryArn' --output text)
        
        echo ""
        print_warning "To import the ECR repository into CDK management, run:"
        echo "npx cdk import iTripSharedStack"
        echo ""
        echo "When prompted, provide:"
        echo "  Resource type: AWS::ECR::Repository"
        echo "  Logical ID: Repository22E53BBD (or similar from CloudFormation template)"
        echo "  Resource identifier: $repo_name"
        echo ""
    else
        print_status "ECR repository '$repo_name' not found - no import needed"
    fi
}

# Function to backup existing Parameter Store parameters
backup_parameters() {
    local environment=$1
    local backup_file="parameter-backup-${environment}-$(date +%Y%m%d-%H%M%S).json"
    
    print_status "Backing up Parameter Store parameters for environment '$environment'..."
    
    aws ssm get-parameters-by-path \
        --path "/itrip/$environment/" \
        --recursive \
        --with-decryption \
        --query 'Parameters' \
        --output json > "$backup_file"
    
    if [ -s "$backup_file" ]; then
        print_success "Parameters backed up to: $backup_file"
        echo "Parameters found:"
        jq -r '.[].Name' "$backup_file" | sed 's/^/  - /'
    else
        print_status "No parameters found for environment '$environment'"
        rm -f "$backup_file"
    fi
}

# Function to restore parameters from backup
restore_parameters() {
    local backup_file=$1
    
    if [ ! -f "$backup_file" ]; then
        print_error "Backup file '$backup_file' not found"
        return 1
    fi
    
    print_status "Restoring parameters from backup file '$backup_file'..."
    
    jq -c '.[]' "$backup_file" | while read -r param; do
        local name=$(echo "$param" | jq -r '.Name')
        local value=$(echo "$param" | jq -r '.Value')
        local type=$(echo "$param" | jq -r '.Type')
        
        print_status "Restoring parameter: $name"
        
        aws ssm put-parameter \
            --name "$name" \
            --value "$value" \
            --type "$type" \
            --overwrite \
            --no-cli-pager
    done
    
    print_success "Parameters restored successfully"
}

# Function to show resource dependencies
show_dependencies() {
    print_status "Resource Dependencies and Import Order:"
    echo ""
    echo "1. Shared Resources (iTripSharedStack):"
    echo "   - ECR Repository (if exists)"
    echo ""
    echo "2. Environment Resources (iTripStagingStack/iTripProductionStack):"
    echo "   - VPC and Networking"
    echo "   - Security Groups"
    echo "   - Load Balancers"
    echo "   - ECS Clusters"
    echo "   - Parameter Store parameters"
    echo ""
    echo "Import Order Recommendation:"
    echo "1. Import shared resources first (ECR repository)"
    echo "2. Import environment-specific resources"
    echo "3. Verify all resources are properly managed"
}

# Function to validate imported resources
validate_imports() {
    local stack_name=$1
    
    print_status "Validating imported resources for stack '$stack_name'..."
    
    # Check if stack exists and is in a good state
    local stack_status=$(aws cloudformation describe-stacks --stack-name "$stack_name" --query 'Stacks[0].StackStatus' --output text 2>/dev/null || echo "NOT_FOUND")
    
    if [ "$stack_status" = "NOT_FOUND" ]; then
        print_error "Stack '$stack_name' not found"
        return 1
    elif [[ "$stack_status" == *"COMPLETE"* ]]; then
        print_success "Stack '$stack_name' is in a healthy state: $stack_status"
    else
        print_warning "Stack '$stack_name' is in state: $stack_status"
    fi
    
    # Show stack resources
    print_status "Resources in stack '$stack_name':"
    aws cloudformation describe-stack-resources --stack-name "$stack_name" --query 'StackResources[].{Type:ResourceType,LogicalId:LogicalResourceId,Status:ResourceStatus}' --output table
}

# Help function
show_help() {
    echo "Usage: $0 [command] [options]"
    echo ""
    echo "Import existing AWS resources into CDK management"
    echo ""
    echo "Commands:"
    echo "  list                           List existing resources that can be imported"
    echo "  generate-import               Generate import commands for ECR repository"
    echo "  backup-params <environment>   Backup Parameter Store parameters"
    echo "  restore-params <backup-file>  Restore parameters from backup"
    echo "  dependencies                  Show resource dependencies and import order"
    echo "  validate <stack-name>         Validate imported resources in a stack"
    echo ""
    echo "Examples:"
    echo "  $0 list"
    echo "  $0 generate-import"
    echo "  $0 backup-params staging"
    echo "  $0 restore-params parameter-backup-staging-20231201-120000.json"
    echo "  $0 dependencies"
    echo "  $0 validate iTripSharedStack"
}

# Main function
main() {
    local command=$1
    local arg1=$2
    
    case "$command" in
        list)
            list_importable_resources
            ;;
        generate-import)
            generate_ecr_import
            ;;
        backup-params)
            if [ -z "$arg1" ]; then
                print_error "Environment is required for backup-params command"
                echo "Usage: $0 backup-params <environment>"
                exit 1
            fi
            backup_parameters "$arg1"
            ;;
        restore-params)
            if [ -z "$arg1" ]; then
                print_error "Backup file is required for restore-params command"
                echo "Usage: $0 restore-params <backup-file>"
                exit 1
            fi
            restore_parameters "$arg1"
            ;;
        dependencies)
            show_dependencies
            ;;
        validate)
            if [ -z "$arg1" ]; then
                print_error "Stack name is required for validate command"
                echo "Usage: $0 validate <stack-name>"
                exit 1
            fi
            validate_imports "$arg1"
            ;;
        -h|--help|"")
            show_help
            ;;
        *)
            print_error "Unknown command: $command"
            show_help
            exit 1
            ;;
    esac
}

# Check if AWS CLI is available
if ! command -v aws &> /dev/null; then
    print_error "AWS CLI is not installed. Please install it first."
    exit 1
fi

# Run main function
main "$@"
