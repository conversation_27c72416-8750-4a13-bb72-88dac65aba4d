#!/bin/bash

# iTrip Infrastructure Cleanup Script
# This script helps clean up failed deployments

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to list CloudFormation stacks
list_stacks() {
    print_status "Listing iTrip CloudFormation stacks..."
    aws cloudformation list-stacks \
        --stack-status-filter CREATE_FAILED ROLLBACK_COMPLETE DELETE_FAILED \
        --query 'StackSummaries[?contains(Stack<PERSON>ame, `iTrip`)].{Name:StackName,Status:StackStatus,Created:CreationTime}' \
        --output table
}

# Function to delete a specific stack
delete_stack() {
    local stack_name=$1
    
    if [ -z "$stack_name" ]; then
        print_error "Stack name is required"
        return 1
    fi
    
    print_warning "Are you sure you want to delete stack '$stack_name'? (y/N)"
    read -r confirmation
    
    if [[ $confirmation =~ ^[Yy]$ ]]; then
        print_status "Deleting stack '$stack_name'..."
        aws cloudformation delete-stack --stack-name "$stack_name"
        
        print_status "Waiting for stack deletion to complete..."
        aws cloudformation wait stack-delete-complete --stack-name "$stack_name"
        
        print_success "Stack '$stack_name' deleted successfully"
    else
        print_status "Stack deletion cancelled"
    fi
}

# Function to delete all iTrip stacks
delete_all_stacks() {
    print_warning "This will delete ALL iTrip stacks. Are you sure? (y/N)"
    read -r confirmation
    
    if [[ $confirmation =~ ^[Yy]$ ]]; then
        local stacks=("iTripProductionStack" "iTripStagingStack" "iTripSharedStack")
        
        for stack in "${stacks[@]}"; do
            if aws cloudformation describe-stacks --stack-name "$stack" &> /dev/null; then
                print_status "Deleting stack '$stack'..."
                aws cloudformation delete-stack --stack-name "$stack"
            else
                print_status "Stack '$stack' does not exist, skipping..."
            fi
        done
        
        # Wait for all deletions to complete
        for stack in "${stacks[@]}"; do
            if aws cloudformation describe-stacks --stack-name "$stack" &> /dev/null; then
                print_status "Waiting for '$stack' deletion to complete..."
                aws cloudformation wait stack-delete-complete --stack-name "$stack" || true
            fi
        done
        
        print_success "All stacks deleted successfully"
    else
        print_status "Stack deletion cancelled"
    fi
}

# Function to clean up failed stacks
cleanup_failed_stacks() {
    print_status "Looking for failed stacks..."
    
    local failed_stacks=$(aws cloudformation list-stacks \
        --stack-status-filter CREATE_FAILED ROLLBACK_COMPLETE DELETE_FAILED \
        --query 'StackSummaries[?contains(StackName, `iTrip`)].StackName' \
        --output text)
    
    if [ -z "$failed_stacks" ]; then
        print_success "No failed stacks found"
        return 0
    fi
    
    print_warning "Found failed stacks: $failed_stacks"
    print_warning "Do you want to delete these failed stacks? (y/N)"
    read -r confirmation
    
    if [[ $confirmation =~ ^[Yy]$ ]]; then
        for stack in $failed_stacks; do
            print_status "Deleting failed stack '$stack'..."
            aws cloudformation delete-stack --stack-name "$stack"
        done
        
        for stack in $failed_stacks; do
            print_status "Waiting for '$stack' deletion to complete..."
            aws cloudformation wait stack-delete-complete --stack-name "$stack" || true
        done
        
        print_success "Failed stacks cleaned up successfully"
    else
        print_status "Cleanup cancelled"
    fi
}

# Function to show stack events for debugging
show_stack_events() {
    local stack_name=$1
    
    if [ -z "$stack_name" ]; then
        print_error "Stack name is required"
        return 1
    fi
    
    print_status "Showing recent events for stack '$stack_name'..."
    aws cloudformation describe-stack-events \
        --stack-name "$stack_name" \
        --query 'StackEvents[0:20].{Time:Timestamp,Status:ResourceStatus,Type:ResourceType,Reason:ResourceStatusReason}' \
        --output table
}

# Help function
show_help() {
    echo "Usage: $0 [command] [options]"
    echo ""
    echo "Clean up iTrip infrastructure resources"
    echo ""
    echo "Commands:"
    echo "  list                    List all iTrip CloudFormation stacks"
    echo "  delete <stack-name>     Delete a specific stack"
    echo "  delete-all             Delete all iTrip stacks"
    echo "  cleanup-failed         Clean up failed stacks"
    echo "  events <stack-name>     Show stack events for debugging"
    echo ""
    echo "Examples:"
    echo "  $0 list"
    echo "  $0 delete iTripSharedStack"
    echo "  $0 delete-all"
    echo "  $0 cleanup-failed"
    echo "  $0 events iTripSharedStack"
}

# Main function
main() {
    local command=$1
    local stack_name=$2
    
    case "$command" in
        list)
            list_stacks
            ;;
        delete)
            delete_stack "$stack_name"
            ;;
        delete-all)
            delete_all_stacks
            ;;
        cleanup-failed)
            cleanup_failed_stacks
            ;;
        events)
            show_stack_events "$stack_name"
            ;;
        -h|--help|"")
            show_help
            ;;
        *)
            print_error "Unknown command: $command"
            show_help
            exit 1
            ;;
    esac
}

# Check if AWS CLI is available
if ! command -v aws &> /dev/null; then
    print_error "AWS CLI is not installed. Please install it first."
    exit 1
fi

# Run main function
main "$@"
