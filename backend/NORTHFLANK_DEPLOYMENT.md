# Northflank Deployment Guide

This guide explains how to deploy the iTrip backend to Northflank using GitHub Actions.

## Prerequisites

1. **Northflank Account**: Create an account at [northflank.com](https://northflank.com)
2. **Northflank Project**: Create a new project in your Northflank dashboard
3. **GitHub Repository**: Ensure your code is in a GitHub repository

## Northflank Setup

### 1. Create a Project

1. Log into your Northflank dashboard
2. Click "Create Project"
3. Choose a project name (e.g., "itrip")
4. Note the **Project ID** - you'll need this for GitHub secrets

### 2. Create Services

Create two services in your Northflank project:

#### Staging Service
1. Click "Create Service" → "Combined Service"
2. Name: `itrip-backend-staging`
3. Choose "Build from repository" or "Deploy from registry"
4. Configure environment variables (see Environment Variables section)
5. Set port to `3000`
6. Note the **Service ID** for GitHub secrets

#### Production Service
1. Click "Create Service" → "Combined Service"
2. Name: `itrip-backend-production`
3. Choose "Build from repository" or "Deploy from registry"
4. Configure environment variables (see Environment Variables section)
5. Set port to `3000`
6. Note the **Service ID** for GitHub secrets

### 3. Generate API Token

1. Go to your Northflank dashboard
2. Click on your profile → "API Tokens"
3. Create a new token with appropriate permissions
4. Copy the token - you'll need this for GitHub secrets

### 4. Registry Access

1. Go to your project settings
2. Navigate to "Registry"
3. Note your registry credentials (username/password)

## GitHub Secrets Configuration

Add the following secrets to your GitHub repository:

### Required Secrets

| Secret Name | Description | Example |
|-------------|-------------|---------|
| `NORTHFLANK_API_TOKEN` | API token from Northflank | `nf_api_...` |
| `NORTHFLANK_PROJECT_ID` | Your Northflank project ID | `project-abc123` |
| `NORTHFLANK_STAGING_SERVICE_ID` | Staging service ID | `service-staging-xyz` |
| `NORTHFLANK_PRODUCTION_SERVICE_ID` | Production service ID | `service-production-xyz` |
| `NORTHFLANK_REGISTRY_USERNAME` | Registry username | Usually your email |
| `NORTHFLANK_REGISTRY_PASSWORD` | Registry password | From project settings |

### How to Add Secrets

1. Go to your GitHub repository
2. Click "Settings" → "Secrets and variables" → "Actions"
3. Click "New repository secret"
4. Add each secret from the table above

## Environment Variables

Configure these environment variables in your Northflank services:

### Common Variables
```
NODE_ENV=staging  # or production
PORT=3000
```

### Database
```
MONGODB_URI=mongodb://...
DATABASE_NAME=itrip
```

### Authentication
```
JWT_SECRET=your-jwt-secret
JWT_EXPIRES_IN=7d
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
```

### External APIs
```
OPENAI_API_KEY=your-openai-key
RESEND_API_KEY=your-resend-key
BRAVE_SEARCH_API_KEY=your-brave-search-key
OPENCAGE_API_KEY=your-opencage-key
```

### App Configuration
```
FRONTEND_URL=https://your-frontend-url.com
BACKEND_URL=https://your-backend-url.northflank.app
```

## Deployment Process

### Automatic Deployment

The GitHub Action will automatically deploy when:

- **Staging**: Push to `develop` branch
- **Production**: Push to `main` branch

### Manual Deployment

You can also trigger deployments manually:

1. Go to GitHub Actions tab
2. Select "Deploy Backend to Northflank"
3. Click "Run workflow"
4. Choose the branch to deploy

## Monitoring and Logs

### Northflank Dashboard

1. Go to your Northflank project
2. Click on the service you want to monitor
3. View logs, metrics, and health status

### Health Checks

The application includes a health check endpoint at `/health` that Northflank will use to monitor service health.

## Troubleshooting

### Common Issues

1. **Build Failures**
   - Check that all required files are in the repository
   - Verify Dockerfile syntax
   - Check build logs in GitHub Actions

2. **Deployment Failures**
   - Verify all GitHub secrets are set correctly
   - Check Northflank service configuration
   - Review deployment logs

3. **Runtime Issues**
   - Check environment variables in Northflank
   - Review application logs
   - Verify external service connectivity

### Getting Help

- Check Northflank documentation: [docs.northflank.com](https://docs.northflank.com)
- Review GitHub Actions logs for detailed error messages
- Check Northflank service logs for runtime issues

## Migration from AWS

The AWS deployment has been disabled by renaming the workflow file. To completely remove AWS resources:

1. Delete the ECS services and task definitions
2. Delete the ECR repository
3. Remove the IAM roles and policies
4. Delete the CloudFormation stacks (if using CDK)

## Next Steps

After successful deployment:

1. Update your frontend configuration to point to the new Northflank URLs
2. Update any external services that call your API
3. Set up monitoring and alerting in Northflank
4. Configure custom domains if needed
