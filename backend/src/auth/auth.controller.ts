/* eslint-disable */

import {
  BadRequestException,
  Body,
  Controller,
  Get,
  Post,
  Req,
  UseGuards,
} from '@nestjs/common';
import { AuthService } from './auth.service';
import { SendVerificationCodeDto, VerifyCodeDto } from './dto/login.dto';
import { GoogleGuard } from './guards/google.guard';
import { Public } from './guards/jwt.guard';

@Controller('auth')
export class AuthController {
  constructor(private authService: AuthService) { }

  @Public()
  @Post('login')
  async sendVerificationCode(@Body() body: SendVerificationCodeDto) {
    try {
      if (!body.email || typeof body.email !== 'string') {
        throw new BadRequestException('Email is required and must be a string');
      }
      return await this.authService.sendVerificationCode(body.email);
    } catch (error) {
      console.error('Error sending verification code:', error);
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new BadRequestException('Failed to send verification code');
    }
  }

  @Public()
  @Post('verify-code')
  async verifyCode(@Body() body: VerifyCodeDto) {
    try {
      if (!body.email || typeof body.email !== 'string') {
        throw new BadRequestException('Email is required and must be a string');
      }
      if (!body.code || typeof body.code !== 'string') {
        throw new BadRequestException('Code is required and must be a string');
      }
      return await this.authService.verifyCode(body.email, body.code);
    } catch (error) {
      console.error('Error verifying code:', error);
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new BadRequestException('Failed to verify code');
    }
  }

  @Public()
  @Post('google')
  async googleAuth(@Body() body: { token: string }) {
    try {
      if (!body.token || typeof body.token !== 'string') {
        throw new BadRequestException('Token is required and must be a string');
      }

      // Validate the user with Google token
      const user = await this.authService.validateGoogleUser({
        token: body.token,
      });

      const { access_token } = await this.authService.login(user);

      return {
        access_token,
        user: {
          id: user._id,
          email: user.email,
          name: user.name,
        },
      };
    } catch (error) {
      console.error('Google auth error:', error);
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new BadRequestException('Failed to authenticate with Google');
    }
  }

  @Public()
  @Get('google/callback')
  @UseGuards(GoogleGuard)
  async googleAuthRedirect(@Req() req: any) {
    try {
      if (!req.user) {
        throw new BadRequestException('No user data received from Google');
      }
      return req.user;
    } catch (error) {
      console.error('Google auth redirect error:', error);
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new BadRequestException('Failed to process Google authentication');
    }
  }

  @Public()
  @Post('check')
  async check(@Body() body: { token: string }) {
    try {
      if (!body.token || typeof body.token !== 'string') {
        throw new BadRequestException('Token is required and must be a string');
      }
      return await this.authService.checkToken(body.token);
    } catch (error) {
      console.error('Error checking token:', error);
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new BadRequestException('Failed to check token');
    }
  }

  @Post('logout')
  async logout() {
    // Since we're using JWT, the main logout is handled client-side
    // This endpoint can be used for any server-side cleanup if needed
    return { message: 'Logged out successfully' };
  }
}
