/* eslint-disable */

import { Injectable, Logger, UnauthorizedException } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { OAuth2Client } from 'google-auth-library';
import { ConfigService } from '../config/config.service';
import { DaysBalanceService } from '../days-balance/days-balance.service';
import { UsersService } from '../users/users.service';

@Injectable()
export class AuthService {
  private readonly logger = new Logger(AuthService.name);
  private readonly googleClient: OAuth2Client;
  private readonly initialDayBalance: number;
  private readonly googleConfig: any;
  private readonly jwtConfig: any;

  constructor(
    private usersService: UsersService,
    private jwtService: JwtService,
    private daysBalanceService: DaysBalanceService,
    private configService: ConfigService,
  ) {
    const googleOAuthConfig = this.configService.getGoogleOAuthConfig();
    this.googleClient = new OAuth2Client(googleOAuthConfig.clientID);
    this.initialDayBalance = 10; // Default to basic plan
    this.googleConfig = this.configService.getGoogleClientConfig();
    this.jwtConfig = this.configService.getJwtConfig();
  }

  async validateUser(username: string): Promise<any> {
    const user = await this.usersService.findOne(username);
    if (user) {
      return user;
    }
    return null;
  }

  async sendVerificationCode(email: string) {
    try {
      await this.usersService.sendVerificationCode(email);
      return true;
    } catch (error) {
      throw new Error('Failed to send verification code');
    }
  }

  async verifyCode(email: string, code: string) {
    try {
      const isVerified = await this.usersService.verifyCode(email, code);
      if (!isVerified) {
        throw new Error('Invalid verification code');
      }
      const user = await this.usersService.findOneByEmail(email);

      if (!user) {
        throw new Error('User not found');
      }

      // Initialize day balance
      const userId = (user._id as any).toString();
      await this.daysBalanceService.initializeUserBalance(userId);
      this.logger.log(
        `Initialized day balance for user ${userId} with basic plan after email verification`,
      );

      return this.login(user);
    } catch (error) {
      this.logger.error(`Failed to verify code: ${error.message}`);
      throw new Error('Failed to verify code');
    }
  }

  async login(user: any) {
    const payload = { email: user.email, sub: user._id };
    return {
      access_token: this.jwtService.sign(payload, this.jwtConfig.signOptions),
      user: {
        _id: user._id,
        email: user.email,
        name: user.name,
      },
    };
  }

  async validateGoogleUser(googleUser: { token: string }) {
    try {
      // 1. Verify the Google token
      const ticket = await this.googleClient.verifyIdToken({
        idToken: googleUser.token,
        audience: [
          this.googleConfig.androidClientId,
          this.googleConfig.iosClientId,
        ],
      });

      const payload = ticket.getPayload();

      // Check if the payload is present
      if (!payload) {
        throw new Error('Invalid token payload');
      }

      // Check if the email is present
      if (!payload.email) {
        throw new Error('Invalid token payload');
      }

      // Check if the email is verified
      if (!payload.email_verified) {
        throw new Error('Email not verified');
      }

      // Check if the token is expired
      if (payload.exp < Date.now() / 1000) {
        throw new Error('Token expired');
      }

      // 2. Find or create a user in your database
      let user = await this.usersService.findOneByEmail(payload.email);
      let isNewUser = false;

      if (!user) {
        user = await this.usersService.create({
          name: payload.name!,
          email: payload.email,
          provider: 'google',
          emailVerified: true,
        });
        isNewUser = true;
      }

      // 3. Initialize day balance for new users
      if (isNewUser && user) {
        const userId = (user._id as any).toString();
        await this.daysBalanceService.initializeUserBalance(userId);
        this.logger.log(
          `Initialized day balance for new Google user ${userId} with basic plan`,
        );

        // 4. Send welcome email for new Google OAuth users
        try {
          await this.usersService.sendWelcomeEmail(user.email, user.name);
          this.logger.log(`Welcome email sent to new Google user: ${user.email}`);
        } catch (error) {
          this.logger.warn(`Failed to send welcome email to Google user ${user.email}: ${error.message}`);
          // Don't fail the authentication if welcome email fails
        }
      }

      // 5. Return the user object
      return user;
    } catch (error) {
      this.logger.error(`Google authentication error: ${error.message}`);
      throw error;
    }
  }

  async checkToken(token: string) {
    try {
      const payload = this.jwtService.verify(
        token,
        this.jwtConfig.verifyOptions,
      );

      if (!payload) {
        throw new Error('Invalid token');
      }

      // Check if the email is present
      if (!payload.email) {
        throw new Error('Invalid token payload');
      }

      const user = await this.usersService.findOneByEmail(payload.email);

      if (!user) {
        throw new Error('User not found');
      }

      return this.login(user);
    } catch (error) {
      // Log the specific error for debugging but don't expose details to client
      if (error.name === 'JsonWebTokenError') {
        this.logger.warn(`Invalid JWT token format: ${error.message}`);
      } else if (error.name === 'TokenExpiredError') {
        this.logger.warn(`Expired JWT token: ${error.message}`);
      } else {
        this.logger.error(`Token verification failed: ${error.message}`);
      }
      throw new UnauthorizedException('Invalid token');
    }
  }
}
