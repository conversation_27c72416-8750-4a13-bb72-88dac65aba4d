#!/bin/bash

# Test Docker build script for iTrip backend

echo "🏗️  Building Docker image for staging..."
docker build -f Dockerfile-staging -t itrip-backend:staging-test .

if [ $? -eq 0 ]; then
    echo "✅ Build successful!"
    echo "🧪 Testing the built image..."
    
    # Test if the main file exists in the container
    docker run --rm itrip-backend:staging-test ls -la dist/src/
    
    echo "📋 Checking if main.js exists..."
    docker run --rm itrip-backend:staging-test test -f dist/src/main.js && echo "✅ main.js found!" || echo "❌ main.js not found!"
    
    echo "🚀 You can now run the container with:"
    echo "docker run --env-file environments/staging.env -p 3000:3000 itrip-backend:staging-test"
else
    echo "❌ Build failed!"
    exit 1
fi
