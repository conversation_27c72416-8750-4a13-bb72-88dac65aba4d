name: Deploy Backend to Northflank

on:
  push:
    branches:
      - main
      - develop
    paths:
      - 'backend/**'
      - '.github/workflows/deploy-backend-northflank.yml'
  pull_request:
    branches:
      - main
    paths:
      - 'backend/**'
      - '.github/workflows/deploy-backend-northflank.yml'

permissions:
  contents: read
  packages: write

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: nasserbvb/itrip-backend

jobs:
  test:
    name: Test Backend
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: ./backend

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '22'
          cache: 'npm'
          cache-dependency-path: backend/package-lock.json

      - name: Install dependencies
        run: npm ci

      # Uncomment when tests are ready
      # - name: Run linting
      #   run: npm run lint

      # - name: Run tests
      #   run: npm run test

      # - name: Run e2e tests
      #   run: npm run test:e2e

  build-and-deploy-staging:
    name: Build and Deploy to Staging
    runs-on: ubuntu-latest
    needs: test
    if: github.event_name == 'push' && github.ref == 'refs/heads/develop'
    environment: staging

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Log in to GitHub Container Registry
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GHCR_TOKEN || secrets.GITHUB_TOKEN }}

      - name: Extract metadata
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}
          tags: |
            type=ref,event=branch,suffix=-{{sha}}
            type=raw,value=staging-latest

      - name: Build and push Docker image
        uses: docker/build-push-action@v5
        with:
          context: ./backend
          file: ./backend/Dockerfile-staging
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          cache-from: type=gha
          cache-to: type=gha,mode=max

      - name: Deploy to Northflank Staging
        uses: northflank/deploy-to-northflank@v1
        with:
          northflank-api-token: ${{ secrets.NORTHFLANK_API_TOKEN }}
          project-id: ${{ secrets.NORTHFLANK_PROJECT_ID }}
          service-id: ${{ secrets.NORTHFLANK_STAGING_SERVICE_ID }}
          image-path: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:staging-latest

  build-and-deploy-production:
    name: Build and Deploy to Production
    runs-on: ubuntu-latest
    needs: test
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'
    environment: production

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Log in to GitHub Container Registry
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GHCR_TOKEN || secrets.GITHUB_TOKEN }}

      - name: Extract metadata
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}
          tags: |
            type=ref,event=branch,suffix=-{{sha}}
            type=raw,value=production-latest
            type=semver,pattern={{version}}
            type=semver,pattern={{major}}.{{minor}}

      - name: Build and push Docker image
        uses: docker/build-push-action@v5
        with:
          context: ./backend
          file: ./backend/Dockerfile-production
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          cache-from: type=gha
          cache-to: type=gha,mode=max

      - name: Deploy to Northflank Production
        uses: northflank/deploy-to-northflank@v1
        with:
          northflank-api-token: ${{ secrets.NORTHFLANK_API_TOKEN }}
          project-id: ${{ secrets.NORTHFLANK_PROJECT_ID }}
          service-id: ${{ secrets.NORTHFLANK_PRODUCTION_SERVICE_ID }}
          image-path: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:production-latest
