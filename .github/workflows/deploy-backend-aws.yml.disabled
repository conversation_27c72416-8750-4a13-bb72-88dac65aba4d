name: Deploy Backend to AWS

on:
  push:
    branches:
      - main
      - develop
    paths:
      - 'backend/**'
      - '.github/workflows/deploy-backend.yml'
  pull_request:
    branches:
      - main
    paths:
      - 'backend/**'
      - '.github/workflows/deploy-backend.yml'

permissions:
  id-token: write
  contents: read

env:
  AWS_REGION: us-east-1
  ECR_REPOSITORY: itrip-backend
  ECS_SERVICE_STAGING: itrip-backend-staging
  ECS_SERVICE_PRODUCTION: itrip-backend-production
  ECS_CLUSTER_STAGING: itrip-staging
  ECS_CLUSTER_PRODUCTION: itrip-production
  ECS_TASK_DEFINITION_STAGING: itrip-backend-staging
  ECS_TASK_DEFINITION_PRODUCTION: itrip-backend-production

jobs:
  test:
    name: Test Backend
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: ./backend

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '22'
          cache: 'npm'
          cache-dependency-path: backend/package-lock.json

      - name: Install dependencies
        run: npm ci

      # - name: Run linting
      #   run: npm run lint

      # - name: Run tests
      #   run: npm run test

      # - name: Run e2e tests
      #   run: npm run test:e2e

  build-and-push-staging:
    name: Build and Push Staging Image to ECR
    runs-on: ubuntu-latest
    needs: test
    if: github.event_name == 'push' && github.ref == 'refs/heads/develop'
    outputs:
      image: ${{ steps.build-image.outputs.image }}

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: ${{ secrets.AWS_ROLE_TO_ASSUME }}
          aws-region: ${{ env.AWS_REGION }}

      - name: Test AWS credentials
        run: |
          aws sts get-caller-identity
          echo "AWS credentials configured successfully"

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v2

      - name: Build, tag, and push staging image to Amazon ECR
        id: build-image
        env:
          ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
          IMAGE_TAG: staging-${{ github.sha }}
        run: |
          cd backend
          echo "🏗️ Building staging image with Dockerfile-staging"
          docker build -f Dockerfile-staging -t $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG .
          docker push $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG
          echo "image=$ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG" >> $GITHUB_OUTPUT

  build-and-push-production:
    name: Build and Push Production Image to ECR
    runs-on: ubuntu-latest
    needs: test
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'
    outputs:
      image: ${{ steps.build-image.outputs.image }}

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: ${{ secrets.AWS_ROLE_TO_ASSUME }}
          aws-region: ${{ env.AWS_REGION }}

      - name: Test AWS credentials
        run: |
          aws sts get-caller-identity
          echo "AWS credentials configured successfully"

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v2

      - name: Build, tag, and push production image to Amazon ECR
        id: build-image
        env:
          ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
          IMAGE_TAG: production-${{ github.sha }}
        run: |
          cd backend
          echo "🏗️ Building production image with Dockerfile-production"
          docker build -f Dockerfile-production -t $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG .
          docker push $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG
          echo "image=$ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG" >> $GITHUB_OUTPUT

  deploy-staging:
    name: Deploy to Staging
    runs-on: ubuntu-latest
    needs: build-and-push-staging
    if: github.ref == 'refs/heads/develop'
    environment: staging

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: ${{ secrets.AWS_ROLE_TO_ASSUME }}
          aws-region: ${{ env.AWS_REGION }}

      - name: Download task definition
        run: |
          aws ecs describe-task-definition --task-definition $ECS_TASK_DEFINITION_STAGING --query taskDefinition > task-definition.json

      # - name: Clean task definition
      #   run: |
      #     # Remove unsupported parameters that may have been added via AWS Console
      #     jq 'del(.enableFaultInjection)' task-definition.json > task-definition-clean.json
      #     mv task-definition-clean.json task-definition.json

      - name: Fill in the new image ID in the Amazon ECS task definition
        id: task-def
        uses: aws-actions/amazon-ecs-render-task-definition@v1
        with:
          task-definition: task-definition.json
          container-name: itrip-backend
          image: ${{ needs.build-and-push-staging.outputs.image }}

      - name: Deploy Amazon ECS task definition
        uses: aws-actions/amazon-ecs-deploy-task-definition@v2
        with:
          task-definition: ${{ steps.task-def.outputs.task-definition }}
          service: ${{ env.ECS_SERVICE_STAGING }}
          cluster: ${{ env.ECS_CLUSTER_STAGING }}
          wait-for-service-stability: true

  deploy-production:
    name: Deploy to Production
    runs-on: ubuntu-latest
    needs: build-and-push-production
    if: github.ref == 'refs/heads/main'
    environment: production

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: ${{ secrets.AWS_ROLE_TO_ASSUME }}
          aws-region: ${{ env.AWS_REGION }}

      - name: Download task definition
        run: |
          aws ecs describe-task-definition --task-definition $ECS_TASK_DEFINITION_PRODUCTION --query taskDefinition > task-definition.json

      # - name: Clean task definition
      #   run: |
      #     # Remove unsupported parameters that may have been added via AWS Console
      #     jq 'del(.enableFaultInjection)' task-definition.json > task-definition-clean.json
      #     mv task-definition-clean.json task-definition.json

      - name: Fill in the new image ID in the Amazon ECS task definition
        id: task-def
        uses: aws-actions/amazon-ecs-render-task-definition@v1
        with:
          task-definition: task-definition.json
          container-name: itrip-backend
          image: ${{ needs.build-and-push-production.outputs.image }}

      - name: Deploy Amazon ECS task definition
        uses: aws-actions/amazon-ecs-deploy-task-definition@v2
        with:
          task-definition: ${{ steps.task-def.outputs.task-definition }}
          service: ${{ env.ECS_SERVICE_PRODUCTION }}
          cluster: ${{ env.ECS_CLUSTER_PRODUCTION }}
          wait-for-service-stability: true
