name: Mobile CD

on:
  push:
    tags:
      - 'v*.*.*'

env:
  APP_VARIANT: production

jobs:
  build:
    name: Build Apps
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: ./mobile
    outputs:
      ios-build-id: ${{ steps.ios-build.outputs.build-id }}
      android-build-id: ${{ steps.android-build.outputs.build-id }}

    steps:
      - name: 🏗 Setup repo
        uses: actions/checkout@v4

      - name: 🏗 Setup Node
        uses: actions/setup-node@v4
        with:
          node-version: 22
          cache: npm
          cache-dependency-path: mobile/package-lock.json

      - name: 🏗 Setup EAS
        uses: expo/expo-github-action@v8
        with:
          expo-version: latest
          eas-version: latest
          token: ${{ secrets.EXPO_TOKEN }}

      - name: 📦 Install dependencies
        run: npm ci

      - name: 🚀 Run Expo install
        run: npx expo install --fix

      - name: 🔧 Run prebuild
        run: npx expo prebuild --clean --no-install

      - name: 🏗 Build iOS
        id: ios-build
        run: |
          BUILD_ID=$(eas build --platform ios --profile production --non-interactive --wait --json | jq -r '.id')
          echo "build-id=$BUILD_ID" >> $GITHUB_OUTPUT
          echo "iOS Build ID: $BUILD_ID"

      - name: 🏗 Build Android
        id: android-build
        run: |
          BUILD_ID=$(eas build --platform android --profile production --non-interactive --wait --json | jq -r '.id')
          echo "build-id=$BUILD_ID" >> $GITHUB_OUTPUT
          echo "Android Build ID: $BUILD_ID"

  submit:
    name: Submit to Stores
    runs-on: ubuntu-latest
    needs: build
    defaults:
      run:
        working-directory: ./mobile

    steps:
      - name: 🏗 Setup repo
        uses: actions/checkout@v4

      - name: 🏗 Setup EAS
        uses: expo/expo-github-action@v8
        with:
          expo-version: latest
          eas-version: latest
          token: ${{ secrets.EXPO_TOKEN }}

      - name: 🚀 Submit to App Store
        run: eas submit --platform ios --profile production --non-interactive --id ${{ needs.build.outputs.ios-build-id }}
        continue-on-error: true

      - name: 🚀 Submit to Play Store
        run: eas submit --platform android --profile production --non-interactive --id ${{ needs.build.outputs.android-build-id }}
        continue-on-error: true

  update:
    name: Publish OTA Update
    runs-on: ubuntu-latest
    needs: build
    defaults:
      run:
        working-directory: ./mobile

    steps:
      - name: 🏗 Setup repo
        uses: actions/checkout@v4

      - name: 🏗 Setup Node
        uses: actions/setup-node@v4
        with:
          node-version: 22
          cache: npm
          cache-dependency-path: mobile/package-lock.json

      - name: 🏗 Setup EAS
        uses: expo/expo-github-action@v8
        with:
          expo-version: latest
          eas-version: latest
          token: ${{ secrets.EXPO_TOKEN }}

      - name: 📦 Install dependencies
        run: npm ci

      - name: 📱 Publish OTA Update
        run: eas update --branch production --message "Release ${{ github.ref_name }}"
